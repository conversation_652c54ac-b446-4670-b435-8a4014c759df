import React, { useState, useEffect } from 'react';
import RightDrawer from './RightDrawer';
import Icon, { IconType } from './Icon';
import PromptHistoryList from './PromptHistoryList';
import PromptVersionSelector from './PromptVersionSelector';
import { usePromptHistory } from '../hooks/usePromptHistory';
import {
  PromptTemplateManager,
  PromptHistoryManager,
  PromptTemplate,
} from '../utils/promptTemplateManager';

import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';
import { CognitiveOptimizedPrompt } from '../prompts/CognitiveOptimizedPrompt';

// 🚀 三阶段增强系统
import { buildThreeStagePrompt } from '../prompts/ModularPromptLoader';

/**
 * Props interface for PromptDrawer component
 * PromptDrawer 组件的属性接口
 */
interface PromptDrawerProps {
  /** Whether the drawer is visible - 是否显示抽屉 */
  isOpen: boolean;
  /** Callback function to close the drawer - 关闭抽屉回调 */
  onClose: () => void;
  /** Current system prompt content - 当前系统提示词 */
  systemPrompt: string;
  /** Callback function to save prompt - 保存提示词回调 */
  onSave: (prompt: string) => void;
  /** Logger instance for status messages - 日志记录器 */
  logger: {
    success: (message: string) => void;
    error: (message: string) => void;
    info: (message: string) => void;
  };
  /** BatchProcessorService instance - 批处理服务实例 */
  batchProcessorService?: EnhancedBatchProcessorService;
}

/**
 * View modes for the prompt drawer
 * 提示词抽屉的视图模式
 */
enum ViewMode {
  EDITOR = 'editor',
  HISTORY = 'history',
  VERSION_SELECTOR = 'versions',
}

/**
 * Supported template types for prompt templates
 * 支持的提示词模板类型
 */
type TemplateType = 'lynx' | 'web';

/**
 * System prompt editing drawer component
 * 系统提示词编辑抽屉组件
 *
 * Features:
 * - Prompt content editing with syntax highlighting
 * - Template management (Lynx and HTML templates)
 * - History tracking and retrieval
 * - Keyboard shortcuts for quick actions
 * - Auto-save functionality
 *
 * 支持功能:
 * - 提示词内容编辑和语法高亮
 * - 模板管理（Lynx 和 HTML 模板）
 * - 历史记录追踪和检索
 * - 快捷键快速操作
 * - 自动保存功能
 */
const PromptDrawer: React.FC<PromptDrawerProps> = ({
  isOpen,
  onClose,
  systemPrompt,
  onSave,
  logger,
  batchProcessorService,
}) => {
  const [currentPrompt, setCurrentPrompt] = useState(systemPrompt);
  const [viewMode, setViewMode] = useState<ViewMode>(ViewMode.EDITOR);


  // 使用提示词历史记录 Hook
  const {
    historyItems,
    isLoading: historyLoading,
    error: historyError,
    addPrompt,
    usePrompt,
    renamePrompt,
    deletePrompt,
    clearHistory,
  } = usePromptHistory();

  /**
   * Synchronize external prompt changes
   * 同步外部提示词变化
   */
  useEffect(() => {
    setCurrentPrompt(systemPrompt);
  }, [systemPrompt]);







  /**
   * Get default template for specified type
   * 获取指定类型的默认模板
   * @param type - Template type (lynx or web)
   * @returns Template content string
   */
  const getDefaultTemplate = (type: TemplateType): string =>
    PromptTemplateManager.getPromptLoaderContent(type);



  /**
   * Handle saving the current prompt
   * 处理保存当前提示词
   * - Saves to external callback
   * - Adds to history if content exists
   * - Closes drawer on success
   */
  const handleSave = async () => {
    try {
      // 保存到外部
      onSave(currentPrompt);

      // 添加到历史记录
      if (currentPrompt.trim()) {
        await addPrompt(currentPrompt.trim());
        PromptHistoryManager.addToHistory(currentPrompt.trim());
      }

      // 检查是否使用了HTML模板并提供针对性指引
      const isHTMLTemplate =
        currentPrompt.includes('HTML 移动端图解生成专家') ||
        currentPrompt.includes('知识可视化专家');

      onClose(); // 保存后关闭抽屉

      if (isHTMLTemplate) {
        logger.success(
          '✅ HTML模板已保存！现在可以在主界面输入查询内容（如"制作购物流程图"），然后点击开始处理按钮',
        );
      } else {
        logger.success('系统提示词已保存');
      }
    } catch (error) {
      logger.error(
        `保存失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  };

  /**
   * Reset to default LYNX template content
   * 重置为默认 LYNX 模板内容
   * Uses the built-in LYNX prompt loader content
   */
  const handleReset = () => {
    const defaultPrompt = PromptTemplateManager.getPromptLoaderContent('lynx');
    setCurrentPrompt(defaultPrompt);
    logger.success('已重置为默认 LYNX 提示词');
  };

  /**
   * Toggle between editor and history view modes
   * 在编辑器和历史记录视图模式之间切换
   */
  const handleToggleView = () => {
    setViewMode(prev => {
      if (prev === ViewMode.EDITOR) return ViewMode.VERSION_SELECTOR;
      if (prev === ViewMode.VERSION_SELECTOR) return ViewMode.HISTORY;
      return ViewMode.EDITOR;
    });
  };

  /**
   * Handle prompt version selection
   * 处理prompt版本选择
   */
  const handlePromptVersionSelect = (content: string, versionName: string) => {
    setCurrentPrompt(content);
    setViewMode(ViewMode.EDITOR);
    logger.success(`${versionName} 已加载到编辑器，请点击"保存提示词"应用`);
  };

  /**
   * Use a prompt from history
   * 使用历史记录中的提示词
   * @param item - History item to use
   */
  const handleUseHistoryPrompt = async (item: {
    id: string;
    content: string;
    title: string;
  }) => {
    try {
      setCurrentPrompt(item.content);
      await usePrompt(item.id);
      setViewMode(ViewMode.EDITOR);
      logger.success(`已切换到提示词: ${item.title}`);
    } catch (error) {
      logger.error(
        `切换提示词失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  };

  /**
   * Handle keyboard shortcuts
   * 处理键盘快捷键
   * - Ctrl+S: Save prompt
   * - Ctrl+R: Reset to default
   * - Ctrl+H: Toggle view mode
   * @param e - Keyboard event
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      if (e.key === 's') {
        e.preventDefault();
        handleSave();
      } else if (e.key === 'r') {
        e.preventDefault();
        handleReset();
      } else if (e.key === 'h') {
        e.preventDefault();
        handleToggleView();
      }
    }
  };

  /**
   * Build header action buttons configuration
   * 构建头部操作按钮配置
   */
  const getViewModeIcon = (): IconType => {
    switch (viewMode) {
      case ViewMode.EDITOR:
        return 'layers' as IconType;
      case ViewMode.VERSION_SELECTOR:
        return 'history' as IconType;
      case ViewMode.HISTORY:
        return 'edit' as IconType;
      default:
        return 'edit' as IconType;
    }
  };

  const getViewModeLabel = (): string => {
    switch (viewMode) {
      case ViewMode.EDITOR:
        return '版本';
      case ViewMode.VERSION_SELECTOR:
        return '历史';
      case ViewMode.HISTORY:
        return '编辑';
      default:
        return '编辑';
    }
  };

  const headerActions = [
    {
      icon: getViewModeIcon(),
      label: getViewModeLabel(),
      onClick: handleToggleView,
      className: 'btn-authority btn-secondary-glass tooltip',
    },
  ];

  return (
    <RightDrawer
      isOpen={isOpen}
      onClose={onClose}
      title={
        <h2 className="enhanced-drawer-title">
          <Icon
            type={
              viewMode === ViewMode.EDITOR
                ? 'edit'
                : viewMode === ViewMode.VERSION_SELECTOR
                  ? 'layers'
                  : 'history'
            }
            color="primary"
            size="sm"
            className="flex-shrink-0"
          />
          <span className="flex-1">
            {viewMode === ViewMode.EDITOR
              ? '智能体提示词设定'
              : viewMode === ViewMode.VERSION_SELECTOR
                ? 'Prompt版本选择'
                : '提示词历史记录'}
          </span>
        </h2>
      }
      width="620px"
      theme="prompt"
      headerActions={headerActions}
    >
      {viewMode === ViewMode.HISTORY ? (
        <PromptHistoryList
          items={historyItems}
          isLoading={historyLoading}
          error={historyError}
          onUsePrompt={handleUseHistoryPrompt}
          onRenamePrompt={renamePrompt}
          onDeletePrompt={deletePrompt}
          onClearHistory={clearHistory}
          logger={logger}
        />
      ) : viewMode === ViewMode.VERSION_SELECTOR ? (
        <PromptVersionSelector
          onPromptSelect={handlePromptVersionSelect}
          logger={logger}
        />
      ) : (
        <div className="h-full flex flex-col">
          {/* 使用提示 */}
          <div className="glass-card-gold">
            <h4 className="typography-body-medium flex items-center mb-3">
              <Icon type="info" color="primary" size="sm" className="mr-2" />
              使用提示
            </h4>
            <ul className="typography-body-small space-y-2 text-gray-600">
              <li>• 系统提示词将应用于所有批量处理的查询</li>
              <li>• 默认使用 PE.md 完整开发规范作为初始提示词</li>
              <li>• 用户可以编辑并保存自定义版本</li>
              <li>• 点击"版本"按钮快速切换不同prompt版本</li>
              <li>• 点击"历史"按钮查看和使用历史提示词</li>
              <li>
                • 快捷键: <kbd>Ctrl+S</kbd> 保存, <kbd>Ctrl+R</kbd> 重置,{' '}
                <kbd>Ctrl+H</kbd> 切换视图
              </li>
            </ul>
          </div>



          {/* 编辑区域 */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="glass-card-gold flex items-center justify-between mb-3">
              <label className="typography-body-large flex items-center gap-2">
                <Icon type="edit" color="primary" size="sm" />
                提示词内容
              </label>
              <div className="flex gap-2">
                <button
                  onClick={() => setCurrentPrompt('')}
                  className="btn-authority btn-secondary-glass text-xs px-3 py-1"
                  title="清空内容"
                >
                  清空
                </button>
                <button
                  onClick={() => {
                    const defaultPrompt =
                      PromptTemplateManager.getPromptLoaderContent('lynx');
                    
                    // 🔍 详细调试信息
                    console.log('🎯 LYNX传统模板按钮点击 - 详细调试信息:');
                    console.log('📊 加载的内容总长度:', defaultPrompt.length);
                    console.log('📄 内容行数:', defaultPrompt.split('\n').length);
                    console.log('🔤 前200字符:', defaultPrompt.substring(0, 200));
                    console.log('🔚 后200字符:', defaultPrompt.substring(defaultPrompt.length - 200));
                    console.log('📋 内容是否包含主要标记:', {
                      hasTitle: defaultPrompt.includes('🎨 Lynx框架世界大师级'),
                      hasComponents: defaultPrompt.includes('组件映射规则'),
                      hasAPI: defaultPrompt.includes('小程序API'),
                      hasEvents: defaultPrompt.includes('事件系统'),
                      hasCanvas: defaultPrompt.includes('Canvas高级绘图')
                    });
                    
                    setCurrentPrompt(defaultPrompt);
                    
                    // 🚀 修复: 设置传统模式，确保处理时不会使用RAG
                    if (batchProcessorService) {
                      batchProcessorService.setTraditionalMode(true);
                      console.log('✅ 已设置传统模式，禁用RAG处理');
                    }
                    
                    logger.success(
                      `✅ 已加载传统 LYNX 模板！长度: ${defaultPrompt.length} 字符 | 行数: ${defaultPrompt.split('\n').length} | 模式：传统模式 | 请点击"保存提示词"后在主界面输入查询内容并开始批处理`,
                    );
                  }}
                  className="text-xs px-4 py-2 rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-lg flex items-center justify-center min-w-24 text-center"
                  style={{
                    background:
                      'linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)',
                    border: '1px solid #c4b5fd',
                    color: '#6b46c1',
                  }}
                  onMouseEnter={e => {
                    const target = e.target as HTMLElement;
                    target.style.background =
                      'linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 30%, #e9d5ff 60%, #ddd6fe 100%)';
                    target.style.transform = 'translateY(-2px) scale(1.02)';
                    target.style.boxShadow =
                      '0 10px 25px rgba(139, 92, 246, 0.25), 0 0 20px rgba(168, 85, 247, 0.15)';
                    target.style.borderColor = '#a855f7';
                    target.style.color = '#581c87';
                  }}
                  onMouseLeave={e => {
                    const target = e.target as HTMLElement;
                    target.style.background =
                      'linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)';
                    target.style.transform = 'translateY(0) scale(1)';
                    target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    target.style.borderColor = '#c4b5fd';
                    target.style.color = '#6b46c1';
                  }}
                  onMouseDown={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(0) scale(0.98)';
                  }}
                  onMouseUp={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(-2px) scale(1.02)';
                  }}
                  title="使用传统的完整 LYNX 小程序模板"
                >
                  <Icon
                    type="mobile"
                    color="purple"
                    size="xs"
                    className="mr-1.5"
                  />
                  传统模板
                </button>

                <button
                  onClick={() => {
                    const htmlPrompt =
                      PromptTemplateManager.getPromptLoaderContent('web');
                    setCurrentPrompt(htmlPrompt);
                    logger.success(
                      '✅ 已加载 HTML 模板！请点击"保存提示词"后在主界面输入查询内容并开始批处理',
                    );
                  }}
                  className="text-xs px-4 py-2 rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-lg flex items-center justify-center min-w-24 text-center"
                  style={{
                    background:
                      'linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)',
                    border: '1px solid #c4b5fd',
                    color: '#6b46c1',
                  }}
                  onMouseEnter={e => {
                    const target = e.target as HTMLElement;
                    target.style.background =
                      'linear-gradient(135deg, #fdf4ff 0%, #f3e8ff 30%, #e9d5ff 60%, #ddd6fe 100%)';
                    target.style.transform = 'translateY(-2px) scale(1.02)';
                    target.style.boxShadow =
                      '0 10px 25px rgba(139, 92, 246, 0.25), 0 0 20px rgba(168, 85, 247, 0.15)';
                    target.style.borderColor = '#a855f7';
                    target.style.color = '#581c87';
                  }}
                  onMouseLeave={e => {
                    const target = e.target as HTMLElement;
                    target.style.background =
                      'linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)';
                    target.style.transform = 'translateY(0) scale(1)';
                    target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    target.style.borderColor = '#c4b5fd';
                    target.style.color = '#6b46c1';
                  }}
                  onMouseDown={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(0) scale(0.98)';
                  }}
                  onMouseUp={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(-2px) scale(1.02)';
                  }}
                  title="使用 HTML 网页生成模板"
                >
                  <Icon
                    type="web"
                    color="purple"
                    size="xs"
                    className="mr-1.5"
                  />
                  HTML模板
                </button>
                <button
                  onClick={() => {
                    const cognitivePrompt = CognitiveOptimizedPrompt.getInstance();
                    const demoQuery = '创建一个现代化的用户界面';
                    const generatedPrompt = cognitivePrompt.generateCognitiveOptimizedPrompt(demoQuery);
                    setCurrentPrompt(generatedPrompt);
                    logger.success(
                      `✅ 已加载认知优化提示词！基于查询: "${demoQuery}" | 长度: ${generatedPrompt.length} 字符 | 请点击"保存提示词"应用`
                    );
                  }}
                  className="text-xs px-4 py-2 rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-lg flex items-center justify-center min-w-24 text-center"
                  style={{
                    background:
                      'linear-gradient(135deg, #fed7e2 0%, #fbb6ce 50%, #f687b3 100%)',
                    border: '1px solid #ed64a6',
                    color: '#97266d',
                  }}
                  onMouseEnter={e => {
                    const target = e.target as HTMLElement;
                    target.style.background =
                      'linear-gradient(135deg, #fef5f7 0%, #fed7e2 30%, #fbb6ce 60%, #f687b3 100%)';
                    target.style.transform = 'translateY(-2px) scale(1.02)';
                    target.style.boxShadow =
                      '0 10px 25px rgba(237, 100, 166, 0.25), 0 0 20px rgba(244, 114, 182, 0.15)';
                    target.style.borderColor = '#d53f8c';
                    target.style.color = '#702459';
                  }}
                  onMouseLeave={e => {
                    const target = e.target as HTMLElement;
                    target.style.background =
                      'linear-gradient(135deg, #fed7e2 0%, #fbb6ce 50%, #f687b3 100%)';
                    target.style.transform = 'translateY(0) scale(1)';
                    target.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                    target.style.borderColor = '#ed64a6';
                    target.style.color = '#97266d';
                  }}
                  onMouseDown={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(0) scale(0.98)';
                  }}
                  onMouseUp={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(-2px) scale(1.02)';
                  }}
                  title="使用认知优化的提示词生成系统"
                >
                  <Icon
                    type="lightbulb"
                    color="primary"
                    size="xs"
                    className="mr-1.5"
                  />
                  认知优化
                </button>
                <button
                  onClick={() => {
                    // 使用三阶段深化增强Ultra版本
                    const threeStagePrompt = buildThreeStagePrompt('创建高质量应用');
                    
                    console.log('🎯 三阶段深化增强Ultra版本按钮点击:');
                    console.log('📊 增强内容总长度:', threeStagePrompt.length);
                    console.log('🔤 增强比例: ~3x标准模式');
                    console.log('⚡ 使用Ultra专业版本，最高token效率');
                    
                    setCurrentPrompt(threeStagePrompt);
                    
                    // 启用三阶段增强模式
                    if (batchProcessorService) {
                      batchProcessorService.setTraditionalMode(false);
                      console.log('✅ 已启用三阶段深化增强模式');
                    }
                    
                    logger.success(
                      `🎯 已加载三阶段深化增强Ultra版本！长度: ${threeStagePrompt.length} 字符 | 模式：三阶段深化 | 效率提升75% | 请点击"保存提示词"后开始批处理`,
                    );
                  }}
                  className="text-xs px-4 py-2 rounded-lg font-medium transition-all duration-300 shadow-sm hover:shadow-lg flex items-center justify-center min-w-24 text-center"
                  style={{
                    background:
                      'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #b3e5fc 100%)',
                    border: '1px solid #87ceeb',
                    color: '#0c4a6e',
                  }}
                  onMouseEnter={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(-1px)';
                    target.style.boxShadow = '0 8px 25px rgba(14, 165, 233, 0.25)';
                  }}
                  onMouseLeave={e => {
                    const target = e.target as HTMLElement;
                    target.style.transform = 'translateY(0)';
                    target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
                  }}
                  title="三阶段深化增强Ultra版本 - 极简专业，token效率最高，Claude4优化"
                >
                  <Icon
                    type="bulb"
                    color="primary" 
                    size="xs"
                    className="mr-1.5"
                  />
                  三阶段Ultra
                </button>
              </div>
            </div>

            <div className="flex-1 glass-card-gold overflow-hidden">
              <textarea
                value={currentPrompt}
                onChange={e => setCurrentPrompt(e.target.value)}
                onKeyDown={handleKeyDown}
                className="form-input-modern w-full h-full resize-none font-mono text-sm leading-relaxed"
                placeholder="请输入系统提示词..."
              />
            </div>

            <div className="glass-card-gold flex items-center justify-between mt-3 text-xs">
              <div className="flex items-center gap-4 text-gray-600">
                <span>
                  字符数:{' '}
                  <span className="text-amber-600 font-semibold">
                    {currentPrompt.length}
                  </span>
                </span>
                <span>
                  行数:{' '}
                  <span className="text-amber-600 font-semibold">
                    {currentPrompt.split('\n').length}
                  </span>
                </span>
                {/* 🔍 内容完整性指示器 */}
                {currentPrompt.length > 50000 && (
                  <span className="flex items-center gap-1.5 text-green-600 font-semibold">
                    <Icon type="check" color="green" size="xs" />
                    完整内容已加载
                  </span>
                )}
                {currentPrompt.length > 0 && currentPrompt.length < 10000 && (
                  <span className="flex items-center gap-1.5 text-orange-500 font-semibold">
                    <Icon type="warning" color="orange" size="xs" />
                    内容可能不完整
                  </span>
                )}
                {currentPrompt.includes('🎨 Lynx框架世界大师级') && (
                  <span className="flex items-center gap-1.5 text-purple-600 font-semibold">
                    <Icon type="mobile" color="purple" size="xs" />
                    LYNX模板
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2 text-gray-500">
                <kbd>Ctrl+S</kbd> 保存 | <kbd>Ctrl+R</kbd> 重置 |{' '}
                <kbd>Ctrl+H</kbd> 历史
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-3 mt-3">
            <button
              onClick={handleSave}
              className="btn--secondary-glass flex-1 h-12 flex items-center justify-center gap-2"
            >
              <Icon type="save" color="white" size="sm" />
              保存提示词
            </button>
            <button
              onClick={handleReset}
              className="btn--secondary-glass flex-1 h-12 flex items-center justify-center gap-2"
            >
              <Icon type="reset" color="white" size="sm" />
              重置内容
            </button>
          </div>
        </div>
      )}
    </RightDrawer>
  );
};

export default PromptDrawer;
