import { useCallback, useEffect, useRef, useState } from 'react';
import { BatchProgress, ProcessResult, BatchConfig } from '../types';
import { EnhancedBatchProcessorService } from '../services/EnhancedBatchProcessorService';

// -----------------------------------------------------------------------------
// useBatchProcessor
// -----------------------------------------------------------------------------
// 该 Hook 为 React 组件提供 batchProcessor 的易用接口：
//   const { start, stop, progress, results, isRunning, retryFailed } = useBatchProcessor();
// 增强版本：添加了重复请求防护、状态锁机制和更好的错误处理
// -----------------------------------------------------------------------------

export function useBatchProcessor() {
  const serviceRef = useRef<EnhancedBatchProcessorService>();

  // 添加请求锁机制，防止重复请求
  const requestLockRef = useRef<boolean>(false);
  const lastRequestTimeRef = useRef<number>(0);

  const [progress, setProgress] = useState<BatchProgress>({
    total: 0,
    completed: 0,
    failed: 0,
    processing: 0,
    pending: 0,
    percentage: 0,
    successPercentage: 0,
    throughput: 0,
    startTime: 0,
  });
  const [results, setResults] = useState<ProcessResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);

  // 初始化 service
  if (!serviceRef.current) {
    serviceRef.current = new EnhancedBatchProcessorService();
    // 监听回调
    serviceRef.current.onProgressUpdate(setProgress);
    serviceRef.current.onResultUpdate(result => {
      setResults(prev => {
        const exists = prev.find(r => r.id === result.id);
        if (exists) {
          // 🔧 关键修复：如果已存在成功的结果，不要覆盖
          if (exists.status === 'success' && result.status !== 'success') {
            console.warn(
              `[useBatchProcessor] 阻止覆盖成功任务结果: ${result.id} (现有状态: ${exists.status}, 新状态: ${result.status})`,
            );
            return prev; // 保持原有的成功结果
          }
          return prev.map(r => (r.id === result.id ? result : r));
        }
        return [...prev, result];
      });
    });
  }

  const start = useCallback(
    async (queries: string[], systemPrompt?: string, config?: BatchConfig) => {
      if (queries.length === 0) {
        console.warn('[useBatchProcessor] 空查询列表，已忽略');
        return;
      }

      // 防重复请求检查
      const now = Date.now();
      if (requestLockRef.current) {
        console.warn('[useBatchProcessor] 请求正在处理中，忽略重复请求');
        return;
      }

      // 防抖检查：如果距离上次请求不足1秒，则忽略
      if (now - lastRequestTimeRef.current < 1000) {
        console.warn('[useBatchProcessor] 请求过于频繁，已忽略');
        return;
      }

      // 状态检查：如果已经在运行，则忽略
      if (isRunning || isRetrying) {
        console.warn(
          '[useBatchProcessor] 批处理任务正在进行中，请等待当前任务完成',
        );
        return;
      }

      // 设置请求锁
      requestLockRef.current = true;
      lastRequestTimeRef.current = now;

      console.log(`[useBatchProcessor] 开始批处理 ${queries.length} 个查询`);

      // 如果提供了系统提示词，则更新
      if (systemPrompt) {
        serviceRef.current!.setSystemPrompt(systemPrompt);
        console.log(
          `[useBatchProcessor] 更新系统提示词，长度: ${systemPrompt.length}`,
        );
      }

      // 🔧 关键修复：如果提供了配置，则更新配置但保护底稿数据模式设置
      if (config) {
        // 🎯 保存当前的底稿数据模式设置
        const currentInternalDataMode =
          serviceRef.current!.getInternalDataMode();

        // 更新配置
        serviceRef.current!.updateConfig(config);

        // 🎯 关键：如果底稿数据模式被覆盖，立即恢复
        if (currentInternalDataMode !== config.processing.useInternalData) {
          console.log(
            `[useBatchProcessor] 🔄 保护底稿数据模式设置: ${currentInternalDataMode} (防止被配置覆盖)`,
          );
          serviceRef.current!.setInternalDataMode(currentInternalDataMode);
        }

        console.log(
          `[useBatchProcessor] 更新完整配置: endpoint=${config.api.endpoint}, workflowId=${config.api.workflowId}, 并发=${config.processing.concurrent}, 速率限制=${config.api.rateLimit}, 底稿数据模式=${serviceRef.current!.getInternalDataMode()}`,
        );
      }

      // 重置状态
      setResults([]);
      setIsRunning(true);

      try {
        await serviceRef.current!.startBatch(queries);
        console.log('[useBatchProcessor] 批处理任务提交成功');
      } catch (error) {
        console.error('[useBatchProcessor] 批处理执行失败:', error);
        // 确保在错误情况下也重置状态
        setIsRunning(false);
        throw error;
      } finally {
        // 释放请求锁
        requestLockRef.current = false;
        console.log('[useBatchProcessor] 请求锁已释放');
      }
    },
    [isRunning, isRetrying],
  );

  const stop = useCallback(() => {
    console.log('[useBatchProcessor] 停止批处理任务');
    serviceRef.current?.stopBatch();
    setIsRunning(false);
    // 释放请求锁
    requestLockRef.current = false;
  }, []);

  // 重试失败的任务
  const retryFailed = useCallback(async () => {
    console.log('🔄 [useBatchProcessor] ===== RETRY FAILED CALLED =====');
    console.log('🔄 [useBatchProcessor] 当前状态:', {
      hasService: !!serviceRef.current,
      isRunning,
      isRetrying,
      requestLock: requestLockRef.current,
    });

    if (!serviceRef.current) {
      console.warn('❌ [useBatchProcessor] 无法重试：服务未初始化');
      return 0;
    }

    if (isRunning) {
      console.warn('❌ [useBatchProcessor] 无法重试：批处理正在运行中');
      return 0;
    }

    if (isRetrying) {
      console.warn('❌ [useBatchProcessor] 无法重试：重试操作正在进行中');
      return 0;
    }

    // 防重复重试检查
    if (requestLockRef.current) {
      console.warn('❌ [useBatchProcessor] 重试请求正在处理中，忽略重复请求');
      return 0;
    }

    // 检查是否有失败的任务
    const currentProgress = serviceRef.current.getProgress();
    console.log('📊 [useBatchProcessor] 当前进度:', currentProgress);

    if (!currentProgress || currentProgress.failed === 0) {
      console.warn('❌ [useBatchProcessor] 没有失败的任务需要重试');
      return 0;
    }

    try {
      requestLockRef.current = true;
      setIsRetrying(true);
      console.log(
        `🚀 [useBatchProcessor] 开始重试 ${currentProgress.failed} 个失败任务`,
      );

      const retriedCount = await serviceRef.current.retryFailedJobs();
      console.log(
        `📈 [useBatchProcessor] 重试结果: ${retriedCount} 个任务被重新入队`,
      );

      if (retriedCount > 0) {
        console.log(
          `✅ [useBatchProcessor] 成功重新入队 ${retriedCount} 个失败任务`,
        );
        // 重试后自动开始处理
        setIsRunning(true);
        console.log('🏃 [useBatchProcessor] 自动启动处理流程');
      } else {
        console.warn('⚠️ [useBatchProcessor] 没有任务被重新入队');
      }

      return retriedCount;
    } catch (error) {
      console.error('💥 [useBatchProcessor] 重试失败:', error);
      throw error; // 重新抛出错误，让上层处理
    } finally {
      setIsRetrying(false);
      requestLockRef.current = false;
      console.log('🔚 [useBatchProcessor] 重试操作完成，清理状态');
    }
  }, [isRunning, isRetrying]);

  // 监听批处理完成状态，确保状态同步
  useEffect(() => {
    if (!serviceRef.current) {
      return;
    }

    const checkCompletion = () => {
      const currentProgress = serviceRef.current?.getProgress();
      if (currentProgress && currentProgress.total > 0) {
        const isCompleted =
          currentProgress.completed + currentProgress.failed >=
          currentProgress.total;
        if (isCompleted && isRunning) {
          console.log('[useBatchProcessor] 批处理任务已完成，更新状态');
          setIsRunning(false);
          requestLockRef.current = false;
        }
      }
    };

    // 定期检查完成状态
    const interval = setInterval(checkCompletion, 1000);
    return () => clearInterval(interval);
  }, [isRunning]);

  // 清理：组件卸载时停止任务
  useEffect(
    () => () => {
      serviceRef.current?.stopBatch();
      requestLockRef.current = false;
    },
    [],
  );

  // 重置所有状态
  const reset = useCallback(() => {
    console.log('[useBatchProcessor] 重置所有状态');
    setResults([]);
    setProgress({
      total: 0,
      completed: 0,
      failed: 0,
      percentage: 0,
      throughput: 0,
      startTime: 0,
    });
    setIsRunning(false);
    setIsRetrying(false);
    requestLockRef.current = false;
    serviceRef.current?.stopBatch();
  }, []);

  // 更新配置
  const updateConfig = useCallback((config: BatchConfig) => {
    if (serviceRef.current) {
      serviceRef.current.updateConfig(config);
      console.log('[useBatchProcessor] 配置已立即更新:', config);
    }
  }, []);

  return {
    progress,
    results,
    isRunning,
    isRetrying,
    start,
    stop,
    retryFailed,
    reset,
    updateConfig,
    service: serviceRef.current, // 新增：暴露service实例
  } as const;
}

export default useBatchProcessor;
