export const LYNX_STYLE_SYSTEM = `🎯 LYNX STYLE SYSTEM RULES (SOURCE-CODE VERIFIED)

=== R1: SELECTOR RULES (源码: lynx-styles/SelectorValidator.ts) ===
RULE: 支持选择器 类型选择器view,text + 类选择器.class + ID选择器#id
RULE: 有限高级选择器 后代.parent view + 子元素.parent>view + 分组h1,h2
RULE: 禁用选择器 通配符*,属性[attr],多类.class1.class2,兄弟+~
RULE: 伪类伪元素 仅支持:not(性能影响) + text组件伪元素(需配置)

=== R2: MULTI-CLASS PROHIBITION (源码: lynx-styles/MultiClassBlocker.ts) ===
RULE: 绝对禁止多类选择器 .class1.class2
RULE: 错误示例 .btn.active,.card.selected
RULE: 正确替代 .btn-active,.card-selected,.active-btn
RULE: 替代模式 .button.primary → .button-primary

=== R3: CORE FEATURES (源码: lynx-styles/CoreFeatures.ts) ===
RULE: 无样式权重 优先级由书写顺序决定，后者覆盖前者
RULE: 无样式继承 默认不继承，可配置enableCSSInheritance部分继承
RULE: 组件样式隔离 默认作用域隔离，仅对组件内部生效
RULE: 性能考量 linear/relative优于flex，避免:not伪类

=== R4: BEST PRACTICES (源码: lynx-styles/BestPractices.ts) ===
RULE: 样式顺序 利用"后来居上"原则控制覆盖关系
RULE: 显式定义 不依赖样式继承，为每个元素显式定义规则
RULE: 颜色定义 字体和背景必须有足够对比度，严禁同色
RULE: 文本居中 flex无法居中text时，添加text-align:center+line-height=height

=== R5: FORBIDDEN CSS PROPERTIES (源码: lynx-styles/ForbiddenProperties.ts) ===
🚨 CRITICAL WARNING: Claude4频繁违反TTSS CSS属性约束，以下规则必须严格执行

RULE: 禁用Webkit私有属性 -webkit-backdrop-filter,-webkit-*全部不支持
RULE: 禁用现代CSS高级属性 backdrop-filter,filter,clip-path,mask,object-fit
RULE: 禁用Grid布局 display:grid,grid-template-*,grid-column,grid-row全部禁止
RULE: 禁用CSS阴影效果 text-shadow,drop-shadow禁止，box-shadow部分支持

常见需要避免的CSS属性：
1. -webkit-backdrop-filter：出现频率极高
2. backdrop-filter：背景模糊效果
3. filter：CSS滤镜效果
4. display: grid：Grid布局
5. user-select：文本选择控制
6. cursor：鼠标样式

=== R6: SUPPORTED CSS PROPERTIES (源码: lynx-styles/SupportedProperties.ts) ===
RULE: 布局相关 display,position,float,overflow,visibility,opacity
RULE: 尺寸相关 width,height,max-width,min-height,box-sizing
RULE: 边距边框 margin,padding,border,border-radius
RULE: 文字样式 font,color,text-align,line-height,text-overflow
RULE: 背景相关 background,background-color,background-image,background-size
RULE: Flexbox布局 flex,flex-direction,justify-content,align-items
RULE: 定位相关 top,right,bottom,left,z-index
RULE: 动画过渡 transition,animation,transform,transform-origin

=== R7: CRITICAL CLAUDE4 RULES (源码: lynx-styles/Claude4Rules.ts) ===
🚨 CRITICAL: 样式系统关键约束规则 (重要技术限制)

RULE: Webkit前缀强制禁用 任何-webkit-开头属性都禁止，特别是-webkit-backdrop-filter
RULE: 现代CSS特性强制禁用 backdrop-filter,filter,mask,clip-path完全禁止
RULE: Grid布局完全禁用 使用Flexbox替代，禁止display:grid
RULE: 用户交互控制禁用 user-select,cursor,pointer-events全部禁止
RULE: 单位系统要求 优先rpx(750rpx=屏幕宽度)，可用px/%/em/rem，禁用vw/vh

=== R8: COLOR SYSTEM RULES (源码: lynx-styles/ColorSystem.ts) ===
RULE: 颜色对比度 文字和背景必须有足够对比度，严禁同色或相近色
RULE: 颜色格式 支持hex,rgb,rgba,预定义颜色名

=== R9: ICON SYSTEM RULES (源码: lynx-icons/IconSystem.ts) ===
RULE: 唯一图标方案 必须且只能使用Font Awesome图标系统
RULE: 禁止Emoji 绝对禁止Unicode Emoji字符
RULE: 禁止替代方案 严禁SVG,PNG,JPG或其他字体图标库

=== R10: TIMELINE PRECISION ALIGNMENT RULES (源码: lynx-styles/TimelineAlignmentValidator.ts) ===
🚨 CRITICAL: 时间轴组件精准对齐约束 (防止样式错乱和对齐偏差)
RULE: 时间轴容器定位一致性 timeline-line和timeline-node必须使用相同定位方式
RULE: 禁止混合定位 timeline-line使用absolute时，timeline-node不能使用flex
RULE: 推荐方案A 全部使用relative定位 + flex布局实现时间轴
RULE: 推荐方案B 全部使用absolute定位 + 精确计算位置
RULE: 错误后果 定位不一致导致节点脱离时间轴，随页面滚动产生错位

🎯 CRITICAL: 时间轴精准对齐强制要求 (像素级精确)
RULE: Icon与Line精准对齐 时间轴icon必须与timeline-line垂直居中对齐，误差≤2rpx
RULE: 序号与背景精准对齐 序号数字必须在背景圆形中完美居中，水平垂直都居中
RULE: 多元素统一对齐 icon、line、序号、背景色必须在同一水平线上精准对齐
RULE: 响应式对齐保持 不同屏幕尺寸下对齐精度必须保持一致
RULE: 滚动对齐稳定 页面滚动时所有时间轴元素必须保持相对位置不变

❌ 错误示例 (定位不一致+对齐偏差):
.timeline-line { position: absolute; left: 50%; }
.timeline-node { display: flex; align-items: center; } /* 节点会脱离时间轴 */
.timeline-icon { margin-top: 5rpx; } /* 错误：icon与line不对齐 */
.timeline-number { text-align: left; } /* 错误：序号未居中 */

✅ 正确示例A (统一relative+精准对齐):
.timeline-container { display: flex; flex-direction: column; position: relative; }
.timeline-line {
  position: absolute; left: 60rpx; top: 0; bottom: 0;
  width: 4rpx; background: #e0e0e0;
}
.timeline-node {
  display: flex; align-items: center; position: relative;
  padding-left: 100rpx; margin: 40rpx 0;
}
.timeline-icon {
  position: absolute; left: 42rpx; /* 精确计算：60rpx - 18rpx/2 = 51rpx */
  width: 36rpx; height: 36rpx;
  display: flex; align-items: center; justify-content: center;
  background: #1976d2; border-radius: 50%;
}
.timeline-number {
  color: white; font-size: 24rpx;
  text-align: center; line-height: 36rpx; /* 与icon高度一致 */
}

✅ 正确示例B (统一absolute+像素级精确):
.timeline-line { position: absolute; left: 60rpx; width: 4rpx; }
.timeline-node { position: absolute; left: 100rpx; }
.timeline-icon {
  position: absolute; left: 42rpx; /* 精准对齐：line中心 - icon半径 */
  width: 36rpx; height: 36rpx; border-radius: 18rpx;
  display: flex; align-items: center; justify-content: center;
}
.timeline-number {
  width: 100%; height: 100%;
  display: flex; align-items: center; justify-content: center;
  font-size: 24rpx; color: white;
}

=== R11: FALLBACK STRATEGIES (源码: lynx-styles/FallbackStrategies.ts) ===
RULE: 背景模糊替代 backdrop-filter:blur() → background-color:rgba()
RULE: 滤镜效果替代 filter:brightness() → opacity
RULE: Grid布局替代 display:grid → display:flex + flex-wrap:wrap
RULE: 文字阴影替代 text-shadow → background-color + padding

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 选择器约束 - 禁用多类选择器+限制高级选择器+性能优先
RULE #2: CSS属性约束 - 禁用webkit前缀+现代CSS特性+Grid布局
RULE #3: 单位颜色约束 - 优先rpx单位+颜色对比度+Font Awesome图标
RULE #4: 定位一致性约束 - 时间轴组件统一定位方式+防止节点脱离
RULE #5: 降级策略 - 禁用效果的替代方案+兼容性保证

THESE RULES ARE MANDATORY FOR FUNCTIONAL TTSS IMPLEMENTATION
`;
