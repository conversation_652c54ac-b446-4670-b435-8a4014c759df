/**
 * Modular Prompt Loader - 模块化提示词加载器
 * 统一管理和加载所有Lynx框架提示词模块，提供完整的开发知识体系
 */

import { LYNX_FRAMEWORK_CORE } from './LynxFrameworkCore';
import { LYNX_COMPONENTS } from './LynxComponents';
import { LYNX_STYLE_SYSTEM } from './LynxStyleSystem';
import { LYNX_UTILS_SYSTEM } from './LynxUtilsSystem';
import { FONT_AWESOME } from './FontAwesome';
import { BEST_PRACTICES } from './BestPractices';
import { THREAD_SYNCHRONIZATION } from './ThreadSynchronization';
import { TTML_STRICT_CONSTRAINTS } from './TTMLStrictConstraints';
import { TTSS_STRICT_CONSTRAINTS } from './TTSSStrictConstraints';
import { LIGHTCHART_PROMPT_CONTENT } from './LightChartPromptLoader';
import { UIGuidance } from './UIGuidance';
import { VISUALIZATION_GUIDANCE } from './VisualizationGuidance';
import { THREE_STAGE_ENHANCEMENT } from './ThreeStageEnhancement';

/**
 * 定义所有提示词模块的名称标识符，用于类型安全和统一管理。
 */
export type PromptModule =
  | 'LynxFrameworkCore'
  | 'LynxComponents'
  | 'LynxStyleSystem'
  | 'LynxUtilsSystem'
  | 'FontAwesome'
  | 'BestPractices'
  | 'ThreadSynchronization'
  | 'TTMLStrictConstraints'
  | 'TTSSStrictConstraints'
  | 'LightChartPromptLoader'
  | 'UIGuidance'
  | 'VisualizationGuidance'
  | 'ThreeStageEnhancement';

export class ModularPromptLoader {
  private static instance: ModularPromptLoader;
  private cache: Map<string, string> = new Map();
  private moduleMap: Record<PromptModule, { name: string; content: string }>;

  private constructor() {
    this.moduleMap = {
      LynxFrameworkCore: {
        name: 'Lynx Framework Core',
        content: LYNX_FRAMEWORK_CORE,
      },
      LynxComponents: {
        name: 'Lynx Components System',
        content: LYNX_COMPONENTS,
      },
      LynxStyleSystem: {
        name: 'Lynx Style System',
        content: LYNX_STYLE_SYSTEM,
      },
      LynxUtilsSystem: {
        name: 'Lynx Utils & LightChart',
        content: LYNX_UTILS_SYSTEM,
      },
      FontAwesome: {
        name: 'Font Awesome Integration',
        content: FONT_AWESOME,
      },
      BestPractices: {
        name: 'Best Practices',
        content: BEST_PRACTICES,
      },
      ThreadSynchronization: {
        name: 'Thread Synchronization',
        content: THREAD_SYNCHRONIZATION,
      },
      TTMLStrictConstraints: {
        name: 'TTML Strict Constraints',
        content: TTML_STRICT_CONSTRAINTS,
      },
      TTSSStrictConstraints: {
        name: 'TTSS Strict Constraints',
        content: TTSS_STRICT_CONSTRAINTS,
      },
      LightChartPromptLoader: {
        name: 'LightChart Integration Guide',
        content: LIGHTCHART_PROMPT_CONTENT,
      },
      UIGuidance: {
        name: 'Unified UI and Visualization Guidance',
        content: UIGuidance,
      },
      VisualizationGuidance: {
        name: 'Knowledge Visualization Guidance',
        content: VISUALIZATION_GUIDANCE,
      },
      ThreeStageEnhancement: {
        name: 'Three-Stage Deep Enhancement',
        content: THREE_STAGE_ENHANCEMENT,
      },
    };
  }

  public static getInstance(): ModularPromptLoader {
    if (!ModularPromptLoader.instance) {
      ModularPromptLoader.instance = new ModularPromptLoader();
    }
    return ModularPromptLoader.instance;
  }

  public getMasterLevelLynxPromptContent(): string {
    const cacheKey = 'master_prompt';

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      const combinedContent = this.buildFullPrompt();
      this.cache.set(cacheKey, combinedContent);
      return combinedContent;
    } catch (error) {
      console.error('Prompt loading failed:', error);
      return this.getFallbackContent();
    }
  }

  /**
   * 构建完整的prompt - 加载所有模块
   */
  public buildFullPrompt(): string {
    const allModules = Object.keys(this.moduleMap) as PromptModule[];

    const sections = allModules.map(moduleName => {
      const module = this.moduleMap[moduleName];
      return `# ${module.name}\n\n${module.content}`;
    });

    return sections.join('\n\n');
  }

  private getFallbackContent(): string {
    return `Lynx框架专家 (Fallback Mode)

你是Lynx框架专家，负责生成完整的Lynx应用代码。

严格输出约束
- 只输出完整的Lynx四件套代码
- 使用<FILES>和<FILE>标签包裹
- 禁止任何解释性文字
- 直接生成可运行的代码

必需文件
- index.ttml：UI结构
- index.ttss：样式设计
- index.js：交互逻辑
- index.json：组件配置

关键约束
- 禁止HTML标签：button、div、span、img等
- 使用Lynx标签：view、text、image、scroll-view
- 禁止CSS属性：webkit前缀、backdrop-filter、grid
- 使用支持属性：flexbox、rpx单位、基础CSS

立即开始生成代码。`;
  }

  public getModuleContent(moduleName: PromptModule): string {
    return (
      this.moduleMap[moduleName]?.content || `Module '${moduleName}' not found`
    );
  }

  public clearCache(): void {
    this.cache.clear();
  }

  public getAvailableModules(): PromptModule[] {
    return Object.keys(this.moduleMap) as PromptModule[];
  }

  // 构建增强版prompt - 支持三阶段深化思考模式
  public buildEnhancedPrompt(
    userQuery: string,
    enableThreeStage: boolean = false,
  ): string {
    if (!enableThreeStage) {
      // 保持向后兼容：返回原有的完整prompt
      return this.getMasterLevelLynxPromptContent();
    }

    // 启用三阶段深化模式
    return this.buildThreeStagePrompt(userQuery);
  }

  // 构建三阶段深化prompt
  public buildThreeStagePrompt(userQuery: string): string {
    const cacheKey = `three_stage_${userQuery}`;

    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      // 获取三阶段增强指引
      const threeStageGuidance = this.getModuleContent('ThreeStageEnhancement');

      // 获取所有核心模块内容（保持原有的专业规范）
      const coreModules = this.buildOptimizedCorePrompt();

      // 构建增强版prompt结构
      const enhancedPrompt = this.assembleThreeStagePrompt(
        userQuery,
        threeStageGuidance,
        coreModules,
      );

      this.cache.set(cacheKey, enhancedPrompt);
      return enhancedPrompt;
    } catch (error) {
      console.error('Three-stage prompt building failed:', error);
      // 降级处理：返回标准prompt
      return this.getMasterLevelLynxPromptContent();
    }
  }

  // 构建优化的核心prompt
  private buildOptimizedCorePrompt(): string {
    // 按照优化的顺序组织核心模块
    const coreModuleOrder: PromptModule[] = [
      'LynxFrameworkCore', // 核心身份和约束
      'UIGuidance', // UI设计规范
      'LynxComponents', // 组件系统
      'LynxStyleSystem', // 样式系统
      'BestPractices', // 最佳实践
      'TTMLStrictConstraints', // TTML约束
      'TTSSStrictConstraints', // TTSS约束
      'LynxUtilsSystem', // 工具系统
      'FontAwesome', // 图标系统
      'ThreadSynchronization', // 线程同步
      'LightChartPromptLoader', // 图表系统
    ];

    const sections = coreModuleOrder.map(moduleName => {
      const module = this.moduleMap[moduleName];
      return `## ${module.name}\n\n${module.content}`;
    });

    return sections.join('\n\n');
  }

  /**
   * 组装三阶段增强prompt
   */
  private assembleThreeStagePrompt(
    userQuery: string,
    threeStageGuidance: string,
    coreModules: string,
  ): string {
    return `${threeStageGuidance}

## 🎯 用户需求
${userQuery}

## 📚 专业知识体系与技术规范
${coreModules}

## 🚀 执行指令
请立即启动三阶段深化思考模式，基于以上用户需求和专业知识体系，产出高质量的Lynx应用代码。

记住：
1. 前两个阶段为内在思考，不输出中间结果
2. 第三阶段直接输出完整的Lynx四件套代码
3. 严格遵循所有技术规范和UI指导原则
4. 确保代码的卓越品质和用户体验`;
  }

  /**
   * 获取增强版prompt统计信息
   */
  public getEnhancedPromptStats(userQuery: string): {
    threeStageEnabled: boolean;
    totalModules: number;
    promptLength: number;
    cacheHit: boolean;
  } {
    const cacheKey = `three_stage_${userQuery}`;
    const cacheHit = this.cache.has(cacheKey);
    const prompt = this.buildThreeStagePrompt(userQuery);

    return {
      threeStageEnabled: true,
      totalModules: Object.keys(this.moduleMap).length,
      promptLength: prompt.length,
      cacheHit,
    };
  }
}

export function getMasterLevelLynxPromptContent(): string {
  const loader = ModularPromptLoader.getInstance();
  return loader.getMasterLevelLynxPromptContent();
}

export function getModuleContent(moduleName: PromptModule): string {
  const loader = ModularPromptLoader.getInstance();
  return loader.getModuleContent(moduleName);
}

export function clearPromptCache(): void {
  const loader = ModularPromptLoader.getInstance();
  loader.clearCache();
}

// 构建增强版prompt（支持三阶段深化模式）
export function buildEnhancedPrompt(
  userQuery: string,
  enableThreeStage: boolean = false,
): string {
  const loader = ModularPromptLoader.getInstance();
  return loader.buildEnhancedPrompt(userQuery, enableThreeStage);
}

// 构建三阶段深化prompt
export function buildThreeStagePrompt(userQuery: string): string {
  const loader = ModularPromptLoader.getInstance();
  return loader.buildThreeStagePrompt(userQuery);
}

// 获取增强版prompt统计信息
export function getEnhancedPromptStats(userQuery: string) {
  const loader = ModularPromptLoader.getInstance();
  return loader.getEnhancedPromptStats(userQuery);
}

export default ModularPromptLoader.getInstance();
