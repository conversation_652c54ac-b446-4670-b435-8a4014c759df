export const TTSS_STRICT_CONSTRAINTS = `
TTSS 严格CSS属性约束规范 - Claude4 必须遵守

CRITICAL WARNING: Claude4 频繁违反TTSS CSS属性约束，以下规则必须严格执行：

ABSOLUTELY FORBIDDEN CSS PROPERTIES - 绝对禁止的CSS属性

所有Webkit属性禁用，这些属性在TTSS中不被支持。

现代CSS高级属性禁用清单 - TTSS作为CSS子集不支持：
- backdrop-filter - 禁止！模糊背景效果不支持
- filter - 禁止！CSS滤镜不支持
- clip-path - 禁止！裁剪路径不支持  
- mask - 禁止！遮罩效果不支持
- object-fit - 禁止！对象适配不支持
- object-position - 禁止！对象定位不支持
- user-select - 禁止！用户选择控制不支持
- pointer-events - 禁止！指针事件控制不支持
- cursor - 禁止！鼠标样式不支持
- resize - 禁止！元素调整大小不支持
- appearance - 禁止！外观控制不支持
- outline - 禁止！轮廓线不支持
- scroll-behavior - 禁止！滚动行为不支持
- overscroll-behavior - 禁止！过度滚动不支持
- scroll-snap-type - 禁止！滚动对齐不支持

Grid布局禁用清单 - TTSS不支持Grid：
- display: grid - 禁止！使用flexbox布局
- grid-template-columns - 禁止！
- grid-template-rows - 禁止！
- grid-template-areas - 禁止！
- grid-column - 禁止！
- grid-row - 禁止！
- grid-area - 禁止！
- grid-gap - 禁止！
- grid-auto-flow - 禁止！
- grid-auto-columns - 禁止！
- grid-auto-rows - 禁止！

CSS选择器禁用清单 - TTSS选择器限制：
- .class1.class2 - 禁止！多类选择器不支持
- [attr=value] - 禁止！属性选择器不支持
- * - 禁止！通配符选择器不支持
- element + element - 禁止！相邻兄弟选择器不支持
- element ~ element - 禁止！通用兄弟选择器不支持



CSS阴影和效果禁用清单：
- text-shadow - 禁止！文本阴影不支持
- box-shadow - 部分支持，但复杂阴影可能有问题
- drop-shadow - 禁止！投影效果不支持

CLAUDE4 最常犯的CSS错误属性：
1. -webkit-backdrop-filter - 出现频率极高
2. backdrop-filter - 背景模糊效果
3. filter - CSS滤镜效果
4. display: grid - Grid布局
5. user-select - 文本选择控制
6. cursor - 鼠标样式

ABSOLUTELY REQUIRED CSS PROPERTIES - 必须使用的正确属性

TTSS完全支持的CSS属性清单：

布局相关：
- display: block, inline, inline-block, flex, none
- position: static, relative, absolute, fixed
- float: left, right, none
- clear: left, right, both, none
- overflow: visible, hidden, scroll, auto
- overflow-x, overflow-y
- visibility: visible, hidden
- opacity: 0-1数值

尺寸相关：
- width, height
- max-width, max-height
- min-width, min-height  
- box-sizing: content-box, border-box

边距边框：
- margin, margin-top, margin-right, margin-bottom, margin-left
- padding, padding-top, padding-right, padding-bottom, padding-left
- border, border-width, border-style, border-color
- border-top, border-right, border-bottom, border-left
- border-radius

文字样式：
- font, font-family, font-size, font-weight, font-style
- color
- text-align: left, center, right, justify
- text-decoration: none, underline, line-through
- line-height
- letter-spacing
- text-overflow: clip, ellipsis

背景相关：
- background, background-color
- background-image
- background-repeat: repeat, no-repeat, repeat-x, repeat-y
- background-position
- background-size: auto, cover, contain, 具体尺寸

Flexbox布局：
- flex, flex-grow, flex-shrink, flex-basis
- flex-direction: row, column, row-reverse, column-reverse
- flex-wrap: nowrap, wrap, wrap-reverse
- justify-content: flex-start, flex-end, center, space-between, space-around
- align-items: flex-start, flex-end, center, stretch, baseline
- align-content: flex-start, flex-end, center, stretch, space-between, space-around
- align-self: auto, flex-start, flex-end, center, baseline, stretch

定位相关：
- top, right, bottom, left
- z-index

动画过渡：
- transition, transition-property, transition-duration, transition-timing-function, transition-delay
- animation, animation-name, animation-duration, animation-timing-function, animation-delay, animation-iteration-count, animation-direction, animation-fill-mode
- transform: translate, rotate, scale, skew等2D变换
- transform-origin

CRITICAL RULES FOR CLAUDE4 - Claude4专用关键规则

Rule 1: Webkit前缀强制禁用
- 任何以 -webkit- 开头的属性都禁止使用
- 使用标准CSS属性替代
- 特别禁止 -webkit-backdrop-filter

Rule 2: 现代CSS特性强制禁用  
- backdrop-filter, filter, mask, clip-path 完全禁止
- Grid布局完全禁止，使用Flexbox替代
- 所有用户交互控制属性禁止（user-select, cursor, pointer-events）
- position: absolute禁止使用

Rule 3: 单位系统强制要求
- 优先使用 rpx 单位（750rpx = 屏幕宽度）
- 可以使用 px, %, em, rem
- 禁止使用 vw, vh, vmin, vmax

Rule 4: 颜色系统强制要求
- 必须为文字和背景设置对比度足够的颜色
- 严禁使用同色或相近色
- 支持 hex, rgb, rgba, 预定义颜色名

COMMON MISTAKES CLAUDE4 MAKES - Claude4常犯错误

错误示例1 - 使用webkit前缀：
WRONG: -webkit-backdrop-filter: blur(10px);
CORRECT: /* TTSS不支持背景模糊，使用其他设计方案 */

错误示例2 - 使用现代CSS特性：
WRONG: filter: blur(5px); backdrop-filter: blur(10px);
CORRECT: /* 使用TTSS支持的opacity和transform实现效果 */

错误示例3 - 使用Grid布局：
WRONG: display: grid; grid-template-columns: 1fr 1fr;
CORRECT: display: flex; flex-direction: row;

错误示例4 - 使用不支持的阴影：
WRONG: text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
CORRECT: /* 使用其他方式增强文字可读性 */

错误示例5 - 使用交互控制属性：
WRONG: user-select: none; cursor: pointer;
CORRECT: /* TTSS不需要这些属性，移动端有自己的交互方式 */

VALIDATION CHECKLIST - 验证清单

在生成TTSS代码前，必须检查：
□ 没有使用任何webkit前缀属性
□ 没有使用 backdrop-filter, filter, mask, clip-path
□ 没有使用Grid布局相关属性
□ 没有使用text-shadow
□ 没有使用user-select, cursor, pointer-events
□ 单位优先使用rpx
□ 颜色对比度足够
□ 只使用TTSS支持的属性清单中的属性

FALLBACK STRATEGIES - 降级策略

当需要被禁止的效果时，使用以下替代方案：

背景模糊效果替代：
FORBIDDEN: backdrop-filter: blur(10px);
ALTERNATIVE: background-color: rgba(255, 255, 255, 0.8);

滤镜效果替代：
FORBIDDEN: filter: brightness(0.8);
ALTERNATIVE: opacity: 0.8;

Grid布局替代：
FORBIDDEN: display: grid;
ALTERNATIVE: display: flex; flex-wrap: wrap;

文字阴影替代：
FORBIDDEN: text-shadow: 2px 2px 4px black;
ALTERNATIVE: background-color: rgba(0,0,0,0.1); padding: 4rpx 8rpx;

ENFORCEMENT - 强制执行

违反任何上述规则的TTSS代码都是错误的，会导致：
1. 样式无法生效
2. 渲染异常
3. 性能问题
4. 跨平台兼容性问题

必须严格遵守TTSS子集约束，没有例外！
`;
