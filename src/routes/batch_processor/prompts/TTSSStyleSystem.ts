export const TTSS_STYLE_SYSTEM = `# TTSS 样式系统规范 v2.0

## � 规则索引
- [P0 核心约束](#p0-核心约束) - 必须遵守，违反导致编译失败
- [P1 重要规范](#p1-重要规范) - 强烈建议，影响性能和维护性  
- [P2 优化建议](#p2-优化建议) - 提升代码质量的建议
- [属性清单](#属性清单) - 完整的允许/禁止属性列表
- [图标系统](#图标系统) - Font Awesome 集成规范
- [检查清单](#检查清单) - 代码生成前的验证步骤

═══════════════════════════════════════════════════════════════

## P0 核心约束

### 1. 选择器严格限制
**基础选择器（允许）**:
- 类型选择器: 'view', 'text'
- 类选择器: '.class-name' 
- ID选择器: '#unique-id'

**高级选择器（有限支持）**:
- 后代选择器: '.parent view' (限制两级嵌套)
- 子元素选择器: '.parent > view' (同样受层级限制)
- 分组选择器: 'h1, h2' (多选择器相同样式)
- 伪类选择器: ':not' (支持但影响性能)
- 伪元素选择器: 仅对 'text' 组件有效，需配置启用

**绝对禁止的选择器**:
- ❌ 通配符选择器 '*'
- ❌ 属性选择器 '[attr=value]'
- ❌ 多类选择器 '.class1.class2' (AI常犯错误)
- ❌ 相邻兄弟选择器 '+'
- ❌ 通用兄弟选择器 '~'

### 2. 文本居中标准方案
**P0 强制规则**: 所有文本居中必须使用双重保障机制

**标准文本居中（用于text元素内容居中）**:
css
.text-center {
  text-align: center;
  line-height: [必须与height数值相等];
}


**容器居中（用于布局容器居中）**:
css
.container-center {
  display: flex;
  align-items: center;
  justify-content: center;
}


**三重保障规则（特殊场景）**:
当需要最高兼容性时，必须同时使用：
css
.triple-center {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: [height值];
}


**具体应用场景**:
- 数字序号容器: 'height: 60rpx; line-height: 60rpx; text-align: center;'
- 标题文本: 'height: 80rpx; line-height: 80rpx; text-align: center;'
- 按钮文字: 'height: 88rpx; line-height: 88rpx; text-align: center;'
- 圆形标号: 'border-radius: 50%; height: 60rpx; line-height: 60rpx; text-align: center;'

**自动检测规则**:
- 检测到 'border-radius: 50%' 必须有 'text-align: center + line-height'
- 检测到固定 'height + width' 必须有 'line-height: [height值]'
- 检测到 flex 居中必须额外添加 'text-align: center'

### 3. 禁用属性严格限制
**WebKit私有属性（全部禁止）**:
- ❌ 任何以 '-webkit-' 开头的属性

**视觉效果属性（全部禁止）**:
- ❌ 'backdrop-filter', 'filter', 'box-shadow', 'text-shadow'
- ❌ 'clip-path', 'mask', 'object-fit'

**布局属性（部分禁止）**:
- ❌ 'grid', 'grid-*' 相关属性
- ❌ 'cursor', 'user-select'

**伪类伪元素（全部禁止）**:
- ❌ ':hover', ':focus', ':nth-child', ':first-child', ':last-child'
- ❌ '::before', '::after', '::first-line', '::first-letter'

**媒体查询（禁止）**:
- ❌ '@media' 查询

═══════════════════════════════════════════════════════════════

## P1 重要规范

### 1. 选择器性能优化
- **优先级**: 类选择器 > 类型选择器 > ID选择器
- **嵌套限制**: 后代选择器建议启用 'enableCSSInvalidation' 配置
- **性能考量**: 避免使用 ':not' 伪类，谨慎使用需额外配置的伪元素

### 2. 布局性能策略
**性能优先级**:
1. 'linear' 布局 (最优)
2. 'relative' 布局 (次优)
3. 'flex' 布局 (复杂场景性能较低)

**配置影响**:
- 启用 'enableCSSInvalidation' 会在DOM变更时触发额外计算
- 若未使用组合器，建议禁用此配置
- 启用 'enableCSSInheritance' 可开启部分属性继承，但不支持 'inherit' 关键字

### 3. 样式系统核心特性
**无样式权重**: 样式优先级由书写顺序决定，后者覆盖前者
**无样式继承**: 默认不继承父元素样式，需显式定义
**组件样式隔离**: 组件样式默认作用域隔离，仅对组件内部生效

### 4. 样式组织最佳实践
- **样式顺序**: 利用"后来居上"原则组织样式文件，控制覆盖关系
- **显式定义**: 不依赖样式继承，为每个元素显式定义规则
- **组件通信**: 通过 props 或全局样式文件在组件间传递样式
- **样式隔离**: 利用组件样式隔离特性，编写高内聚、低耦合的组件

═══════════════════════════════════════════════════════════════

## P2 优化建议

### 1. 命名规范
- **选择器命名**: 使用小写字母和连字符 '.main-content'
- **语义化**: 避免缩写，保持语义清晰 '.navigation' 而非 '.nav'

### 2. 单位系统
- **优先使用**: 'rpx' 单位，基于 750rpx 等于设备屏幕宽度进行自适应缩放
- **基准规则**: 750rpx = 设备屏幕宽度

### 3. 颜色对比度要求
- **强制要求**: 必须为字体和背景明确设定有足够对比度的颜色
- **严禁同色**: 避免使用同色或相近颜色，防止内容不可见
- **对比度标准**: 文字背景对比度建议 4.5:1 以上

═══════════════════════════════════════════════════════════════

## 属性清单

### 允许使用的CSS属性

**布局相关**:
'display', 'position', 'float', 'clear', 'overflow', 'clip', 'visibility', 'opacity'

**尺寸相关**:
'width', 'height', 'max-width', 'max-height', 'min-width', 'min-height', 'box-sizing'

**边距与边框**:
'margin', 'padding', 'border', 'border-radius' 及其各方向的细分属性 ('margin-top', 'border-left' 等)

**文字样式**:
'font', 'color', 'text-align', 'text-decoration', 'line-height', 'letter-spacing', 'text-overflow', 'text-transform'

**背景样式**:
'background', 'background-color', 'background-image', 'background-repeat', 'background-position', 'background-size'

**Flexbox布局**:
'flex', 'flex-direction', 'justify-content', 'align-items', 'align-self', 'flex-wrap', 'flex-grow', 'flex-shrink', 'flex-basis'

**定位相关**:
'top', 'right', 'bottom', 'left', 'z-index'

**动画相关**:
'transition', 'animation', 'transform', 'transform-origin' 及其相关属性

### 禁止使用的属性（完整列表）

**WebKit私有属性**: 任何以 '-webkit-' 开头的属性
**视觉效果**: 'backdrop-filter', 'filter', 'box-shadow', 'text-shadow', 'clip-path', 'mask', 'object-fit'
**交互相关**: 'cursor', 'user-select', 'pointer-events'
**Grid布局**: 'grid', 'grid-template-columns', 'grid-template-rows', 'grid-area' 等所有grid相关属性
**其他禁用**: 所有未在允许列表中明确列出的属性

═══════════════════════════════════════════════════════════════

## 图标系统

### P0 强制规范
- **唯一方案**: 必须且只能使用 Font Awesome 作为项目唯一图标系统
- **实现细节**: 遵循 'design/FontAwesome.ts' 文件指南
- **绝对禁止**: Unicode Emoji 字符
- **严禁替代**: SVG, PNG, JPG 或其他字体图标库

═══════════════════════════════════════════════════════════════

## 检查清单

### P0 必检项（违反=编译失败）
- [ ] **选择器合规**: 是否避免了多类选择器 '.class1.class2'？
- [ ] **文本居中完整**: 所有居中文本是否同时包含 'text-align: center' 和 'line-height = height'？
- [ ] **禁用属性检查**: 是否避免了所有 '-webkit-' 前缀和禁用属性？
- [ ] **图标系统**: 是否只使用了 Font Awesome，避免了 Emoji？

### P1 重要检查（影响性能）
- [ ] **布局选择**: 是否选择了合适的布局方案（linear > relative > flex）？
- [ ] **样式继承**: 是否为每个元素显式定义了样式规则？
- [ ] **选择器性能**: 是否避免了性能较差的 ':not' 伪类？
- [ ] **配置优化**: 是否根据实际使用情况配置了 'enableCSSInvalidation'？

### P2 质量提升（代码质量）
- [ ] **命名规范**: 是否使用了语义化的小写连字符类名？
- [ ] **单位统一**: 是否优先使用了 'rpx' 单位？
- [ ] **颜色对比**: 是否确保了足够的文字背景对比度？
- [ ] **代码组织**: 是否遵循了样式书写顺序控制优先级？

### 特殊场景检查
- [ ] **圆形元素**: 'border-radius: 50%' 是否配套了文本居中规则？
- [ ] **固定尺寸**: 固定 'height + width' 是否设置了对应的 'line-height'？
- [ ] **Flex居中**: 使用 flex 居中是否额外添加了 'text-align: center'？

═══════════════════════════════════════════════════════════════

## 降级策略与替代方案

当需要实现被禁用的效果时，使用以下替代方案：

**背景模糊效果**:
css
/* 替代 backdrop-filter: blur(10px) */
.blur-effect {
  background-color: rgba(255, 255, 255, 0.8);
}


**滤镜效果**:
css
/* 替代 filter: brightness(0.8) */
.dim-effect {
  opacity: 0.8;
}


**Grid布局**:
css
/* 替代 display: grid */
.grid-alternative {
  display: flex;
  flex-wrap: wrap;
}


**文字阴影**:
css
/* 替代 text-shadow: 2px 2px 4px rgba(0,0,0,0.5) */
.text-shadow-alternative {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}


**Box阴影**:
css
/* 替代 box-shadow: 0 4px 8px rgba(0,0,0,0.1) */
.shadow-alternative {
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
}


═══════════════════════════════════════════════════════════════

## 性能考量与配置建议

### 性能影响因素
1. **布局性能**: 'linear' 和 'relative' 布局通常优于 'flex' 布局
2. **配置影响**: 启用 'enableCSSInvalidation' 会在 DOM 变更时触发额外计算
3. **选择器性能**: 避免使用 ':not' 伪类，谨慎使用需额外配置的伪元素

### 推荐配置
javascript
// 推荐的样式系统配置
{
  enableCSSInvalidation: false, // 未使用组合器时建议禁用
  enableCSSInheritance: false,  // 默认禁用，需要时可开启
  enablePseudoElements: false   // 默认禁用，仅text组件需要时开启
}


### 调试与优化
- 利用组件样式隔离特性进行问题定位
- 通过书写顺序控制样式优先级，避免使用 '!important'
- 定期检查样式文件，移除未使用的规则`;
