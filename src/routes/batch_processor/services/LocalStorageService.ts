// -----------------------------------------------------------------------------
// LocalStorageService.ts
// -----------------------------------------------------------------------------
// 该服务负责本地持久化存储和读取：
//  1. 系统提示词(System Prompt)
//  2. 处理结果历史记录
//  3. 用户配置(User Preferences)
// -----------------------------------------------------------------------------

import { ProcessResult, PromptTemplate, HistoryStats } from '../types';
import { formatTime } from '../utils/timeFormatter';
import { getPEPromptContent } from '../utils/pePromptLoader';

/**
 * 存储键常量
 */
const STORAGE_KEYS = {
  SYSTEM_PROMPT: 'batchProcessor.systemPrompt',
  HISTORY: 'batchProcessor.history',
  PREFERENCES: 'batchProcessor.preferences',
  PROMPT_TEMPLATES: 'batchProcessor.promptTemplates',
  LAST_SESSION: 'batchProcessor.lastSession',
};

/**
 * 默认系统提示词 - 使用 MasterLevelUIPromptLoader 的内容
 */
export const DEFAULT_SYSTEM_PROMPT = getPEPromptContent();

/**
 * 历史记录过滤类型
 */
export type HistoryFilterType = 'all' | 'success' | 'error';

/**
 * 历史记录排序类型
 */
export type HistorySortType =
  | 'date-desc'
  | 'date-asc'
  | 'time-desc'
  | 'time-asc';

/**
 * 历史记录查询参数
 */
export interface HistoryQueryParams {
  filter?: HistoryFilterType;
  sort?: HistorySortType;
  search?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 分页历史记录结果
 */
export interface PagedHistory {
  items: ProcessResult[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  stats: HistoryStats;
}

/**
 * 本地存储服务
 */
export class LocalStorageService {
  /**
   * 检查当前环境是否支持LocalStorage
   */
  private static isAvailable(): boolean {
    try {
      const testKey = '__test__';
      localStorage.setItem(testKey, testKey);
      localStorage.removeItem(testKey);
      return true;
    } catch (e) {
      console.warn('[LocalStorageService] 本地存储不可用');
      return false;
    }
  }

  /**
   * 保存数据到LocalStorage
   */
  private static save<T>(key: string, data: T): boolean {
    try {
      if (!this.isAvailable()) {
        return false;
      }

      const serialized = JSON.stringify(data);
      localStorage.setItem(key, serialized);
      return true;
    } catch (error) {
      console.error(`[LocalStorageService] 保存到'${key}'失败:`, error);
      return false;
    }
  }

  /**
   * 从LocalStorage读取数据
   */
  private static load<T>(key: string, defaultValue: T): T {
    try {
      if (!this.isAvailable()) {
        return defaultValue;
      }

      const serialized = localStorage.getItem(key);
      if (serialized === null) {
        return defaultValue;
      }

      return JSON.parse(serialized) as T;
    } catch (error) {
      console.error(`[LocalStorageService] 从'${key}'读取失败:`, error);
      return defaultValue;
    }
  }

  /**
   * 保存系统提示词
   */
  static saveSystemPrompt(prompt: string): boolean {
    console.log('[LocalStorageService] 保存系统提示词');
    return this.save(STORAGE_KEYS.SYSTEM_PROMPT, prompt);
  }

  /**
   * 加载系统提示词
   */
  static loadSystemPrompt(): string {
    console.log('[LocalStorageService] 加载系统提示词');
    return this.load(STORAGE_KEYS.SYSTEM_PROMPT, DEFAULT_SYSTEM_PROMPT);
  }

  /**
   * 保存处理结果历史 - 使用LRU策略，最多保存50条记录
   * @param history 最近的处理结果数组
   * @param maxItems 最大保存条目数（默认50）
   */
  static saveHistory(history: ProcessResult[], maxItems = 50): boolean {
    console.log(
      `[LocalStorageService] 保存历史记录 (${history.length}项) - LRU策略，最大${maxItems}条`,
    );

    try {
      // 加载现有历史记录
      const existingHistory = this.loadHistory();

      // 使用Map来实现LRU逻辑
      const historyMap = new Map<string, ProcessResult>();

      // 先加载现有记录（按时间顺序，最新的在前）
      existingHistory.forEach(item => {
        historyMap.set(item.id, item);
      });

      // 添加新记录，如果已存在则更新（这会自动移到最新位置）
      history.forEach(item => {
        // 如果记录已存在，先删除再添加，确保在Map中的位置是最新的
        if (historyMap.has(item.id)) {
          historyMap.delete(item.id);
        }
        historyMap.set(item.id, item);
      });

      // 转换回数组并按时间排序（最新的在前）
      const allHistory = Array.from(historyMap.values()).sort(
        (a, b) => b.startTime - a.startTime,
      );

      // LRU策略：限制历史记录数量，保留最新的maxItems条
      const trimmed = allHistory.slice(0, maxItems);

      console.log(
        `[LocalStorageService] LRU处理完成：保留${trimmed.length}条记录，丢弃${Math.max(0, allHistory.length - maxItems)}条旧记录`,
      );

      // 注意：最近会话跟踪已被移除，不再需要单独保存会话标识

      return this.save(STORAGE_KEYS.HISTORY, trimmed);
    } catch (error) {
      console.error('[LocalStorageService] 保存历史记录失败:', error);
      return false;
    }
  }

  /**
   * 加载处理结果历史
   * @param params 查询参数
   */
  static loadHistory(params?: HistoryQueryParams): ProcessResult[] {
    console.log('[LocalStorageService] 加载历史记录');
    const allHistory = this.load<ProcessResult[]>(STORAGE_KEYS.HISTORY, []);

    if (!params) {
      return allHistory;
    }

    // 应用过滤
    let filtered = allHistory;

    // 状态过滤
    if (params.filter && params.filter !== 'all') {
      filtered = filtered.filter(item => item.status === params.filter);
    }

    // 搜索过滤
    if (params.search) {
      const searchLower = params.search.toLowerCase();
      filtered = filtered.filter(
        item =>
          item.query.toLowerCase().includes(searchLower) ||
          item.playgroundUrl?.toLowerCase().includes(searchLower) ||
          item.error?.toLowerCase().includes(searchLower),
      );
    }

    // 排序
    if (params.sort) {
      switch (params.sort) {
        case 'date-asc':
          filtered.sort((a, b) => a.startTime - b.startTime);
          break;
        case 'date-desc':
          filtered.sort((a, b) => b.startTime - a.startTime);
          break;
        case 'time-asc':
          filtered.sort((a, b) => (a.processTime || 0) - (b.processTime || 0));
          break;
        case 'time-desc':
          filtered.sort((a, b) => (b.processTime || 0) - (a.processTime || 0));
          break;
      }
    }

    return filtered;
  }

  /**
   * 获取分页历史记录
   * @param params 查询参数
   */
  static getPagedHistory(params: HistoryQueryParams = {}): PagedHistory {
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;

    // 获取过滤后的所有记录
    const filtered = this.loadHistory(params);
    const total = filtered.length;
    const totalPages = Math.ceil(total / pageSize);

    // 计算分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, total);
    const items = filtered.slice(startIndex, endIndex);

    // 计算统计信息
    const stats = this.getHistoryStats(filtered);

    return {
      items,
      total,
      page,
      pageSize,
      totalPages,
      stats,
    };
  }

  /**
   * 获取历史记录统计
   * @param items 可选的历史记录项目，如不提供则加载全部
   */
  static getHistoryStats(items?: ProcessResult[]): HistoryStats {
    const history = items || this.loadHistory();

    const totalRecords = history.length;
    const successfulQueries = history.filter(
      item => item.status === 'success',
    ).length;
    const failedQueries = history.filter(
      item => item.status === 'error',
    ).length;
    const totalQueries = history.length;
    const successRate =
      totalQueries > 0 ? (successfulQueries / totalQueries) * 100 : 0;

    return {
      totalRecords,
      totalQueries,
      successfulQueries,
      failedQueries,
      successRate,
    };
  }

  // 已移除未使用的会话和ID查询方法：
  // - saveLastSession()
  // - getLastSessionResults()
  // - getResultById()

  /**
   * 清除历史记录
   */
  static clearHistory(): boolean {
    console.log('[LocalStorageService] 清除历史记录');
    return this.save(STORAGE_KEYS.HISTORY, []);
  }
}

export default LocalStorageService;
