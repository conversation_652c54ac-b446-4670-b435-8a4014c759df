/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📝 UNIFIED FORM SYSTEM - 统一表单系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了design-system/forms.css + input-area-fix.css
 * 提供完整的表单组件解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces design-system/forms.css + input-area-fix.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BASE FORM STYLES - 基础表单样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.form {
  display: flex;
  flex-direction: column;
  gap: var(--form-gap-md);
}

/* 表单区块 - 优化间距 */
.form-section {
  margin-bottom: 20px;
}

.form-section:last-child {
  margin-bottom: 0;
}

/* 表单组 - 紧凑布局 */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 14px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group.full-width {
  width: 100%;
}

/* 双列布局 - 优化间距 */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 14px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* 数字输入卡片 */
.form-group.numeric-card {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.08);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.form-group.numeric-card:hover {
  border-color: rgba(59, 130, 246, 0.15);
  background: rgba(255, 255, 255, 0.7);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏷️ FORM LABELS - 表单标签
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  cursor: pointer;
  transition: color 0.2s ease;
}

.form-label:hover {
  color: var(--color-primary-600);
}

.form-label.required::after {
  content: '*';
  color: var(--color-error-500);
  margin-left: var(--space-1);
}

/* 增强表单标签 */
.enhanced-form-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-600);
  margin-bottom: var(--space-2);
  cursor: pointer;
  transition: color 0.2s ease;
}

.enhanced-form-label:hover {
  color: var(--color-primary-600);
}

.enhanced-form-label.required::after {
  content: '*';
  color: var(--color-error-500);
  margin-left: var(--space-1);
}

/* 字段提示 */
.field-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 4px;
  opacity: 0.8;
}

/* 表单描述 */
.form-description {
  font-size: 0.8125rem;
  color: var(--text-secondary);
  margin-top: var(--space-1);
  line-height: 1.4;
}

/* 表单提示 */
.form-hint {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  margin-top: var(--space-1);
  font-style: italic;
}

/* 区块标题 - 优化视觉层次 */
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  padding: 8px 0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  position: relative;
}

.section-title::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-secondary-500));
  border-radius: 1px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📝 INPUT FIELDS - 输入字段
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-input {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--input-bg);
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.15);
  background: var(--color-white);
}

.form-input:hover:not(:focus) {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06), inset 0 1px 2px rgba(0, 0, 0, 0.02);
}

.form-input:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-input::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

/* 增强输入框 */
.enhanced-form-input {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.06), inset 0 1px 2px rgba(59, 130, 246, 0.02);
}

.enhanced-form-input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 12px rgba(59, 130, 246, 0.15);
  background: var(--color-white);
}

.enhanced-form-input:hover:not(:focus) {
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.08), inset 0 1px 2px rgba(59, 130, 246, 0.02);
}

.enhanced-form-input:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.enhanced-form-input::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

/* 现代输入框 */
.form-input-modern {
  width: 100%;
  padding: var(--input-padding-md);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.5;
  box-sizing: border-box;
}

.form-input-modern:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
  background: var(--color-white);
}

.form-input-modern:hover:not(:focus) {
  border-color: var(--color-primary-300);
}

/* 数字输入框 */
.form-input.numeric {
  text-align: center;
  font-weight: 600;
  color: var(--color-primary-700);
  border: none;
  background: transparent;
}

.enhanced-form-input.numeric {
  text-align: center;
  font-weight: 600;
  color: var(--color-primary-700);
  border: none;
  background: transparent;
}

/* 特殊输入框 - 查询输入 */
.query-input-textarea {
  transition: all 0.2s ease !important;
  background: transparent !important;
  border: none;
  resize: none;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  width: 100%;
  min-height: 120px;
  padding: var(--space-3);
}

.query-input-textarea:focus {
  outline: none !important;
  background: transparent !important;
}

.query-input-textarea::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📄 TEXTAREA - 文本域
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-textarea {
  width: 100%;
  padding: var(--input-padding-md);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--input-bg);
  transition: all 0.2s ease;
  line-height: 1.6;
  min-height: 120px;
  resize: vertical;
  box-sizing: border-box;
}

.form-textarea:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: var(--shadow-focus-blue);
  background: var(--color-white);
}

.form-textarea:hover:not(:focus) {
  border-color: var(--color-gray-400);
}

.form-textarea:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-textarea::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

.enhanced-form-textarea {
  width: 100%;
  padding: var(--input-padding-md);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.6;
  min-height: 120px;
  resize: vertical;
  box-sizing: border-box;
}

.enhanced-form-textarea:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
  background: var(--color-white);
}

.enhanced-form-textarea:hover:not(:focus) {
  border-color: var(--color-primary-300);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📦 SELECT FIELDS - 选择字段
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-select {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: var(--input-bg);
  transition: all 0.2s ease;
  line-height: 1.5;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  box-sizing: border-box;
}

.form-select:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: var(--shadow-focus-blue);
  background-color: var(--color-white);
}

.form-select:hover:not(:focus) {
  border-color: var(--color-gray-400);
}

.form-select:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.enhanced-form-select {
  width: 100%;
  padding: var(--input-padding-sm) var(--input-padding-md);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.2s ease;
  line-height: 1.5;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  box-sizing: border-box;
}

.enhanced-form-select:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
  background-color: var(--color-white);
}

.enhanced-form-select:hover:not(:focus) {
  border-color: var(--color-primary-300);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ☑️ CHECKBOXES & RADIOS - 复选框和单选框
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-checkbox,
.form-radio {
  width: 16px;
  height: 16px;
  border: 2px solid var(--color-gray-300);
  background: var(--color-white);
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.form-checkbox {
  border-radius: 4px;
}

.form-radio {
  border-radius: 50%;
}

.form-checkbox:checked,
.form-radio:checked {
  background: var(--color-primary-500);
  border-color: var(--color-primary-500);
}

.form-checkbox:checked::before {
  content: '✓';
  display: block;
  color: white;
  text-align: center;
  font-size: 12px;
  line-height: 12px;
  font-weight: bold;
}

.form-radio:checked::before {
  content: '';
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  margin: 3px;
}

.form-checkbox:focus,
.form-radio:focus {
  outline: none;
  box-shadow: var(--shadow-focus-blue);
}

.form-checkbox:hover,
.form-radio:hover {
  border-color: var(--color-primary-400);
}

/* 复选框组合标签 */
.checkbox-group,
.radio-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  cursor: pointer;
  user-select: none;
}

.checkbox-group:hover .form-checkbox,
.radio-group:hover .form-radio {
  border-color: var(--color-primary-400);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎛️ TOGGLE SWITCHES - 切换开关
 * ═══════════════════════════════════════════════════════════════════════════ */

.toggle-wrapper {
  position: relative;
}

.toggle-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-100);
  border-radius: 10px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.toggle-label:hover {
  background: var(--color-primary-100);
  border-color: var(--color-primary-200);
}

.toggle-content {
  flex: 1;
  margin-right: var(--space-4);
}

.toggle-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-1) 0;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.toggle-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: var(--color-gray-200);
  border-radius: 12px;
  transition: all 0.3s ease;
  flex-shrink: 0;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-slider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: var(--color-white);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.toggle-input:checked + .toggle-label .toggle-switch {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.toggle-input:checked + .toggle-label .toggle-slider {
  transform: translateX(20px);
  box-shadow: var(--shadow-md);
}

/* 增强切换开关 */
.enhanced-toggle-wrapper {
  position: relative;
}

.enhanced-toggle-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.enhanced-toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  background: var(--color-primary-50);
  border: 1px solid var(--color-primary-100);
  border-radius: 10px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
}

.enhanced-toggle-label:hover {
  background: var(--color-primary-100);
  border-color: var(--color-primary-200);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔢 NUMERIC INPUTS - 数字输入框
 * ═══════════════════════════════════════════════════════════════════════════ */

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper.numeric {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-primary-200);
  transition: all 0.3s ease;
}

.input-wrapper.numeric:hover {
  border-color: var(--color-primary-300);
}

.input-wrapper.numeric:focus-within {
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
}

.input-wrapper.numeric.enhanced {
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-primary-200);
  transition: all 0.3s ease;
}

.input-wrapper.numeric.enhanced:hover {
  border-color: var(--color-primary-300);
}

.input-wrapper.numeric.enhanced:focus-within {
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-focus-blue);
}

.input-unit {
  position: absolute;
  right: var(--space-4);
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-gray-500);
  pointer-events: none;
  background: rgba(255, 255, 255, 0.9);
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
}

.input-status-indicator {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-success-500);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.input-wrapper:focus-within .input-status-indicator {
  opacity: 1;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 💬 FORM HINTS & ERRORS - 表单提示和错误
 * ═══════════════════════════════════════════════════════════════════════════ */

.form-hint,
.field-hint {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-weight: 500;
}

.form-error {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--color-error-600);
  font-weight: 500;
}

.form-success {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--color-success-600);
  font-weight: 500;
}

.form-warning {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--input-margin);
  font-size: 0.75rem;
  color: var(--color-warning-600);
  font-weight: 500;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 FORM STATES - 表单状态
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 错误状态 */
.form-input.error,
.enhanced-form-input.error {
  border-color: var(--color-error-500);
  box-shadow: var(--shadow-focus-red);
}

.form-input.error:focus,
.enhanced-form-input.error:focus {
  border-color: var(--color-error-600);
  box-shadow: var(--shadow-focus-red);
}

/* 成功状态 */
.form-input.success,
.enhanced-form-input.success {
  border-color: var(--color-success-500);
  box-shadow: var(--shadow-focus-green);
}

.form-input.success:focus,
.enhanced-form-input.success:focus {
  border-color: var(--color-success-600);
  box-shadow: var(--shadow-focus-green);
}

/* 警告状态 */
.form-input.warning,
.enhanced-form-input.warning {
  border-color: var(--color-warning-500);
  box-shadow: var(--shadow-focus-amber);
}

.form-input.warning:focus,
.enhanced-form-input.warning:focus {
  border-color: var(--color-warning-600);
  box-shadow: var(--shadow-focus-amber);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 FORM LAYOUTS - 表单布局
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 网格布局 */
.form-grid {
  display: grid;
  gap: var(--grid-gap-md);
}

.form-grid-2 {
  display: grid;
  gap: var(--grid-gap-md);
  grid-template-columns: repeat(2, 1fr);
}

.form-grid-3 {
  display: grid;
  gap: var(--grid-gap-md);
  grid-template-columns: repeat(3, 1fr);
}

.form-grid-4 {
  display: grid;
  gap: var(--grid-gap-md);
  grid-template-columns: repeat(4, 1fr);
}

/* 数字网格 */
.numeric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: var(--grid-gap-md);
  margin-top: var(--space-4);
}

.numeric-grid.enhanced {
  gap: var(--grid-gap-lg);
}

/* 切换开关网格 */
.toggle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--grid-gap-md);
  margin-top: var(--space-4);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 SPECIAL FORM FIXES - 特殊表单修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 查询输入卡片修复 */
.query-input-card.optimized-card {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(148, 163, 184, 0.15) !important;
  border-radius: 12px !important;
}

.query-input-card.optimized-card:hover {
  background: rgba(255, 255, 255, 1) !important;
  border-color: rgba(35, 146, 239, 0.25) !important;
}

/* 确保内部元素不会覆盖边框 */
.query-input-card > * {
  position: relative;
  z-index: 1;
}

/* 动画增强 */
.query-input-card,
.query-preview-card {
  will-change: transform, box-shadow, border-color;
}

/* 防止动画卡顿 */
.query-input-card *,
.query-preview-card * {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1024px) {
  .numeric-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: var(--grid-gap-sm);
  }
  
  .toggle-grid {
    grid-template-columns: 1fr;
    gap: var(--grid-gap-sm);
  }
  
  .form-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .form-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .numeric-grid {
    grid-template-columns: 1fr;
    gap: var(--grid-gap-xs);
  }
  
  .form-grid-2,
  .form-grid-3,
  .form-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .form-input,
  .enhanced-form-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  /* 移动端圆角适配 */
  .query-input-card,
  .query-preview-card {
    border-radius: 10px !important;
  }
  
  .query-input-card .rounded-lg,
  .query-input-card .rounded,
  .query-preview-card .rounded-lg,
  .query-preview-card .rounded {
    border-radius: 6px !important;
  }
}

/* 小屏幕hover效果调整 */
@media (hover: none) {
  .query-input-card:hover,
  .query-preview-card:hover {
    transform: none !important;
  }
  
  .form-label:hover,
  .enhanced-form-label:hover {
    color: var(--text-primary);
  }
  
  .form-input:hover,
  .enhanced-form-input:hover,
  .form-select:hover,
  .enhanced-form-select:hover {
    border-color: var(--input-border);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 焦点可见性 */
.form-input:focus-visible,
.enhanced-form-input:focus-visible,
.form-select:focus-visible,
.enhanced-form-select:focus-visible,
.form-textarea:focus-visible,
.enhanced-form-textarea:focus-visible,
.form-checkbox:focus-visible,
.form-radio:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .form-input,
  .enhanced-form-input,
  .form-select,
  .enhanced-form-select,
  .form-textarea,
  .enhanced-form-textarea,
  .toggle-switch,
  .toggle-slider,
  .input-wrapper,
  .form-label,
  .enhanced-form-label {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .form-input,
  .enhanced-form-input,
  .form-select,
  .enhanced-form-select,
  .form-textarea,
  .enhanced-form-textarea {
    border-width: 2px;
  }
  
  .toggle-switch {
    border: 2px solid var(--color-gray-600);
  }
  
  .form-checkbox,
  .form-radio {
    border-width: 3px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .form-input,
  .enhanced-form-input,
  .form-select,
  .enhanced-form-select,
  .form-textarea,
  .enhanced-form-textarea {
    min-height: 44px;
  }
  
  .form-checkbox,
  .form-radio {
    min-width: 44px;
    min-height: 44px;
  }
  
  .toggle-switch {
    min-width: 44px;
    min-height: 44px;
  }
}