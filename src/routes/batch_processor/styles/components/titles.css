/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 UNIFIED TITLE SYSTEM - 统一标题系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 为批处理器页面提供统一的标题样式系统
 * 确保左栏、右栏和各个组件的标题风格一致
 *
 * @version 3.0 - 重构版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces unified-titles.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 KEYFRAME ANIMATIONS - 关键帧动画
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主标题渐变动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏷️ TITLE HIERARCHY - 标题层级系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主标题 - 用于页面顶部主要标题 */
.unified-title-primary {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-primary-700);
  background: linear-gradient(
    135deg,
    var(--color-blue-700) 0%,
    var(--color-primary-500) 25%,
    var(--color-blue-500) 50%,
    var(--color-gold-500) 75%,
    var(--color-gold-600) 100%
  );
  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 10px rgba(35, 146, 239, 0.2);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: gradientShift 8s ease infinite;
}

/* 次级标题 - 用于各个区块的标题 */
.unified-title-secondary {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-primary-700);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

/* 小标题 - 用于组件内部标题 */
.unified-title-tertiary {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.4;
  color: var(--color-primary-800);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

/* 微小标题 - 用于最小的标识性文字 */
.unified-title-quaternary {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.4;
  color: var(--color-primary-900);
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 TITLE ICON CONTAINERS - 标题图标容器
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主标题图标容器 */
.unified-title-icon-primary {
  width: 3rem;
  height: 3rem;
  min-width: 3rem;
  min-height: 3rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  /* 确保图标可见 */
  color: white;
}

.unified-title-icon-primary svg,
.unified-title-icon-primary .semi-icon {
  color: white !important;
}

.unified-title-icon-primary svg path,
.unified-title-icon-primary svg g {
  stroke: white !important;
}

/* 次级标题图标容器 */
.unified-title-icon-secondary {
  width: 2rem;
  height: 2rem;
  min-width: 2rem;
  min-height: 2rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 2px 8px rgba(59, 130, 246, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  /* 确保图标可见 */
  color: white;
}

.unified-title-icon-secondary svg,
.unified-title-icon-secondary .semi-icon {
  color: white !important;
}

.unified-title-icon-secondary svg path,
.unified-title-icon-secondary svg g {
  stroke: white !important;
}

/* 小标题图标容器 */
.unified-title-icon-tertiary {
  width: 1.5rem;
  height: 1.5rem;
  min-width: 1.5rem;
  min-height: 1.5rem;
  border-radius: 0.375rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 1px 4px rgba(59, 130, 246, 0.15),
    0 1px 2px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  /* 确保图标可见 */
  color: white;
}

.unified-title-icon-tertiary svg,
.unified-title-icon-tertiary .semi-icon {
  color: white !important;
}

.unified-title-icon-tertiary svg path,
.unified-title-icon-tertiary svg g {
  stroke: white !important;
}

/* 微小标题图标容器 */
.unified-title-icon-quaternary {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.25rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 1px 2px rgba(59, 130, 246, 0.1);
  flex-shrink: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 ICON CONTAINER SYSTEM - 图标容器系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 通用图标容器基础类 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  box-shadow:
    0 2px 8px rgba(59, 130, 246, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
  /* 确保图标可见 */
  color: white;
  /* 修复脉冲动画定位问题 */
    position: relative;
}

.icon-container svg,
.icon-container .semi-icon {
  color: white !important;
}

.icon-container svg path,
.icon-container svg g {
  stroke: white !important;
}

/* 图标容器尺寸变体 */
.icon-container--xs {
  width: 1rem;
  height: 1rem;
  border-radius: 0.25rem;
}

.icon-container--sm {
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 0.25rem;
}

.icon-container--md {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.375rem;
}

.icon-container--lg {
  width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    padding: 0.5rem 0;
    /* 增加上下 padding */
  }

  /* 快速开始步骤卡片中的图标容器 - 保持原始尺寸 */
  .quick-start-card .icon-container--lg {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  padding: 0;
}

/* 特定针对快速开始步骤中的序列号图标 - 减小一倍尺寸 */
.grid.grid-cols-1.md\:grid-cols-3.gap-6.mb-10 .icon-container.icon-container--lg {
  width: 2rem !important;
  height: 2rem !important;
  border-radius: 0.5rem !important;
  padding: 0 !important;
}
.icon-container--xl {
  width: 5rem;
  height: 5rem;
  border-radius: 1.25rem;
  padding: 0.75rem 0;
  /* 增加上下 padding */
}

.icon-container--2xl {
  width: 3rem;
  height: 3rem;
  border-radius: 1rem;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ✨ ENHANCED EFFECTS - 增强效果
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 悬停效果 */
.unified-title-secondary:hover,
.unified-title-tertiary:hover {
  color: var(--color-blue-800);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

.unified-title-secondary:hover .unified-title-icon-secondary,
.unified-title-tertiary:hover .unified-title-icon-tertiary,
.unified-title-secondary:hover .icon-container,
.unified-title-tertiary:hover .icon-container {
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.3),
    0 2px 6px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
  transition: all 0.2s ease;
}

/* 焦点状态 */
.unified-title-primary:focus-visible,
.unified-title-secondary:focus-visible,
.unified-title-tertiary:focus-visible {
  outline: 2px solid var(--color-blue-500);
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 THEMED VARIANTS - 主题变体
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 成功主题标题 */
.unified-title-success {
  color: var(--color-success);
}

.unified-title-success .unified-title-icon-secondary,
.unified-title-success .unified-title-icon-tertiary,
.unified-title-success .icon-container {
  background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-light) 100%);
}

/* 警告主题标题 */
.unified-title-warning {
  color: var(--color-warning);
}

.unified-title-warning .unified-title-icon-secondary,
.unified-title-warning .unified-title-icon-tertiary,
.unified-title-warning .icon-container {
  background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-light) 100%);
}

/* 错误主题标题 */
.unified-title-error {
  color: var(--color-error);
}

.unified-title-error .unified-title-icon-secondary,
.unified-title-error .unified-title-icon-tertiary,
.unified-title-error .icon-container {
  background: linear-gradient(135deg, var(--color-error) 0%, var(--color-error-light) 100%);
}

/* 中性主题标题 */
.unified-title-neutral {
  color: var(--color-gray-600);
}

.unified-title-neutral .unified-title-icon-secondary,
.unified-title-neutral .unified-title-icon-tertiary,
.unified-title-neutral .icon-container {
  background: linear-gradient(135deg, var(--color-gray-500) 0%, var(--color-gray-600) 100%);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔗 LEGACY COMPATIBILITY - 向后兼容
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 确保与现有title-text类的兼容性 */
.title-text {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-primary-700);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

/* 渐变文字类兼容性 */
.gradient-text,
.gradient-text-enhanced {
  background: linear-gradient(
    135deg,
    var(--color-blue-700) 0%,
    var(--color-primary-500) 25%,
    var(--color-blue-500) 50%,
    var(--color-gold-500) 75%,
    var(--color-gold-600) 100%
  );
  background-size: 200% auto;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0px 2px 10px rgba(35, 146, 239, 0.2);
  animation: gradientShift 8s ease infinite;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 FORCED CONSISTENCY - 强制一致性 (确保所有小标题完全统一)
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 统一的小标题样式 */
.unified-title-secondary {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-primary-700);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

/* 统一的小标题图标容器样式 */
.unified-title-icon-secondary {
  width: 2rem;
  height: 2rem;
  min-width: 2rem;
  min-height: 2rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 2px 8px rgba(59, 130, 246, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  color: white;
}

.unified-title-icon-secondary svg,
.unified-title-icon-secondary .semi-icon {
  color: white;
}

.unified-title-icon-secondary svg path,
.unified-title-icon-secondary svg g {
  stroke: white;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 14寸笔记本屏幕优化 - 暂时禁用以确保小标题一致性 */
@media (min-width: 1366px) and (max-width: 1919px) {
  .unified-title-primary {
    font-size: 1.25rem;
  }
  
  /* 禁用小标题的响应式调整以确保一致性
  .unified-title-secondary {
    font-size: 0.875rem;
  }
  */
  
  .unified-title-tertiary {
    font-size: 0.8rem;
  }
  
  .unified-title-icon-primary,
  .icon-container--2xl {
    width: 2.5rem;
    height: 2.5rem;
  }
  
  /* 禁用小标题图标的响应式调整以确保一致性
  .unified-title-icon-secondary,
  .icon-container--lg {
    width: 1.75rem;
    height: 1.75rem;
  }
  */
}

/* 平板和小屏设备 - 暂时禁用小标题调整以确保一致性 */
@media (max-width: 768px) {
  .unified-title-primary {
    font-size: 1.125rem;
    gap: 0.5rem;
  }
  
  /* 禁用小标题的响应式调整以确保一致性
  .unified-title-secondary {
    font-size: 0.875rem;
    gap: 0.375rem;
  }
  */
  
  .unified-title-tertiary {
    font-size: 0.75rem;
    gap: 0.25rem;
  }
  
  .unified-title-icon-primary,
  .icon-container--2xl {
    width: 2rem;
    height: 2rem;
    border-radius: 0.5rem;
  }
  
  /* 禁用小标题图标的响应式调整以确保一致性
  .unified-title-icon-secondary,
  .icon-container--lg {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.375rem;
  }
  */
  
  .unified-title-icon-tertiary,
  .icon-container--md {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 0.25rem;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 SPECIFIC ELEMENT FIXES - 特定元素修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主要内容区域的大图标容器 */
#main-content .icon-container--2xl {
  width: 3rem;
  height: 3rem;
  min-width: 3rem;
  min-height: 3rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  color: white;
}

#main-content .icon-container--2xl svg,
#main-content .icon-container--2xl .semi-icon {
  color: white;
}

#main-content .icon-container--2xl svg path,
#main-content .icon-container--2xl svg g {
  stroke: white;
}

/* 简化的图标容器样式 */
.text-center .icon-container,
.py-8.mb-8 .icon-container {
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  color: white;
}

.text-center .icon-container svg,
.py-8.mb-8 .icon-container svg,
.text-center .icon-container .semi-icon,
.py-8.mb-8 .icon-container .semi-icon {
  color: white;
}

.text-center .icon-container svg path,
.py-8.mb-8 .icon-container svg path,
.text-center .icon-container svg g,
.py-8.mb-8 .icon-container svg g {
  stroke: white;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 ICON CONTRAST FIXES - 图标对比度修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 图标容器内的图标颜色 - 简化版本 */
.icon-container,
.unified-title-icon-primary,
.unified-title-icon-secondary,
.unified-title-icon-tertiary,
.unified-title-icon-quaternary {
  color: white;
}

.icon-container svg,
.unified-title-icon-primary svg,
.unified-title-icon-secondary svg,
.unified-title-icon-tertiary svg,
.unified-title-icon-quaternary svg {
  color: currentColor;
}

/* 🌟 IconRating 图标处理 - 简洁蓝色背景 + 呼吸动效 */

/* 呼吸动画 */
@keyframes icon-breathe {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
  }
}

/* IconRating容器样式 */
.icon-rating-container {
  /* 蓝色渐变背景 */
  background: linear-gradient(135deg, var(--color-blue-500) 0%, var(--color-blue-700) 100%);
  border-radius: 1rem;
  /* 呼吸动画 */
    animation: icon-breathe 3s ease-in-out infinite;
    /* 确保图标居中 */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* IconRating图标本身 */
.icon-rating-container .semi-icon,
.icon-rating-container svg {
  color: white;
  position: relative;
  z-index: 1;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .unified-title-primary {
    animation: none;
  }
  
  .gradient-text,
  .gradient-text-enhanced {
    animation: none;
  }

    /* 禁用IconRating的呼吸动画 - 可访问性考虑 */
    .icon-rating-container {
      animation: none !important;
  }
}