<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重试失败项按钮修复测试</title>
    <link rel="stylesheet" href="./styles/foundation/base.css">
    <link rel="stylesheet" href="./styles/components/buttons.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 40px;
            line-height: 1.6;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin: 0 0 20px 0;
            color: #1e293b;
            font-size: 18px;
            font-weight: 600;
        }
        
        .button-row {
            display: flex;
            gap: 16px;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .description {
            font-size: 14px;
            color: #64748b;
            margin-top: 8px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .before, .after {
            padding: 16px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .before h4, .after h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .before h4 {
            color: #dc2626;
        }
        
        .after h4 {
            color: #16a34a;
        }
        
        /* 旧版本按钮样式（用于对比） */
        .old-retry-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 25%, #fde68a 50%, #fcd34d 75%, #f59e0b 100%);
            color: #92400e;
            border: 1px solid rgba(245, 158, 11, 0.3);
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .old-retry-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.25);
        }
        
        .old-retry-btn svg {
            width: 16px;
            height: 16px;
        }
        
        .old-retry-btn .old-badge {
            background: rgba(146, 64, 14, 0.2);
            color: #92400e;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 重试失败项按钮修复测试</h1>
        <p>测试修复后的按钮布局、图标显示和徽章样式</p>
        
        <div class="test-section">
            <h3>✅ 修复后的按钮（推荐使用）</h3>
            <div class="button-row">
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">10</span>
                </button>
                
                <button class="btn btn-sm btn--primary-gold retry-failed-btn opacity-50 cursor-not-allowed" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试中...</span>
                    <span class="retry-failed-badge">10</span>
                </button>
            </div>
            <div class="description">
                ✅ 使用CSS Grid布局确保完美对齐<br>
                ✅ 图标尺寸统一为16x16px<br>
                ✅ 徽章样式优化，使用等宽字体<br>
                ✅ 移除冗余的Tailwind类，避免样式冲突
            </div>
        </div>
        
        <div class="comparison">
            <div class="before">
                <h4>❌ 修复前的问题</h4>
                <button class="old-retry-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span>重试失败项</span>
                    <span class="old-badge">10</span>
                </button>
                <ul style="font-size: 12px; margin: 12px 0 0 0; color: #dc2626;">
                    <li>样式冲突：Tailwind + 自定义CSS</li>
                    <li>布局错乱：space-x-2 与 gap 冲突</li>
                    <li>图标尺寸：h-4 w-4 与CSS冲突</li>
                    <li>徽章样式：过多重复类名</li>
                </ul>
            </div>
            
            <div class="after">
                <h4>✅ 修复后的改进</h4>
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">10</span>
                </button>
                <ul style="font-size: 12px; margin: 12px 0 0 0; color: #16a34a;">
                    <li>CSS Grid布局：完美对齐</li>
                    <li>统一样式：移除冲突类名</li>
                    <li>图标优化：专用retry-icon类</li>
                    <li>徽章简化：单一样式定义</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎨 不同状态测试</h3>
            <div class="button-row">
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">5</span>
                </button>
                
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">123</span>
                </button>
            </div>
            <div class="description">
                测试不同数字长度的徽章显示效果
            </div>
        </div>
    </div>
</body>
</html>
