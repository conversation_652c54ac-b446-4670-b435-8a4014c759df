/**
 * Canvas高级应用和绘图系统部分
 */

export const CANVAS_SYSTEM_PROMPT = `Canvas 高级应用详解（按需选择）

🔍 文档分工说明：
- 本文件：Canvas系统架构、生命周期、设备像素比(DPR)处理权威指南、错误防范、API限制
- LynxCanvasAudio.ts：Canvas+Audio集成、音频可视化技术
- 两文件互补，避免重复，DPR处理以本文件为准

🚨🚨🚨 CRITICAL: Claude4 Canvas高频错误强制防范 🚨🚨🚨

**错误案例1 - 错误的Canvas初始化方式 (Claude4最常犯错误)**：
❌ 绝对禁止的错误代码：
\`\`\`javascript
// 错误！使用了错误的API
const canvas = lynx.createCanvasNG("routeCanvas");  // ❌ 错误：直接传参数
if (!canvas) return;

const ctx = canvas.getContext("2d");
const width = canvas.width;
const height = canvas.height;

// 错误！没有resize事件监听
// 错误！没有attachToCanvasView绑定
// 错误！没有pixelRatio适配

ctx.clearRect(0, 0, width, height);
ctx.fillStyle = "#f8f9fa";
ctx.fillRect(0, 0, width, height);
\`\`\`

✅ 强制要求的正确代码：
\`\`\`javascript
// 正确！使用正确的Canvas初始化流程
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    // 1. 创建未绑定的Canvas Element（无参数）
    const canvas = lynx.createCanvasNG();  // ✅ 正确：无参数创建

    // 2. 必须！设置resize事件监听（在绑定前）
    canvas.addEventListener('resize', ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      // 3. 必须！pixelRatio适配
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      console.log('Canvas setup complete, starting animation...');
      // 4. 重绘逻辑
      this.startAnimation();
    });

    // 5. 必须！绑定到Canvas View
    canvas.attachToCanvasView('canvas-llm');  // ✅ 正确：使用name属性绑定
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
},

drawSilkRoadMap(ctx, width, height) {
  // 清空画布
  ctx.clearRect(0, 0, width, height);

  // 绘制背景
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);

  // 其他绘制逻辑...
}
\`\`\`

**强制检查清单 - Canvas初始化必须包含的4个步骤**：
□ 1. lynx.krypton.createCanvasNG() - 无参数创建
□ 2. addEventListener('resize') - resize事件监听
□ 3. SystemInfo.pixelRatio - 高分屏适配
□ 4. attachToCanvasView(name) - 绑定到Canvas View

**违反后果**：缺少任何一步都会导致Canvas渲染失败、尺寸错误、模糊显示或无法显示！

🚨 CRITICAL: Lynx Canvas 查询规则
**重要提醒**：在 Lynx 框架中，Canvas 元素的查询语法与标准 Web API 不同！
- ❌ 错误：使用 CSS ID 选择器 '#canvas-id'
- ✅ 正确：使用 Lynx 专用选择器 'canvasId=canvas-id'
- 核心原因：canvas-id 属性对应 canvasId= 查询语法，这是 Lynx 框架的特有实现

Canvas 优先策略：
当选择Canvas时，应完全使用Canvas实现所有视觉渲染和交互：
- 完全使用Canvas元素绘制界面，而非DOM节点
- 禁止出现文字重叠
- 所有视觉效果、动画和交互都应在Canvas内实现
- 仅使用最少必要的view元素作为容器
- 严格限制，所有js功能全部写在canvas的执行内容里面

Canvas 渲染核心规范：

Canvas基础渲染：
- 状态管理：save()/restore()隔离，避免样式冲突
- 像素精确：devicePixelRatio适配，清晰显示
- 性能优先：局部重绘，requestAnimationFrame控制
- 内存优化：及时清理，复用对象

🚨 Canvas生命周期管理 (强制4步骤，缺一不可)：

**步骤1 - 创建Canvas Element**：
- ✅ 正确：lynx.krypton.createCanvasNG() - 无参数创建
- ❌ 错误：lynx.createCanvasNG("canvasName") - 禁止传参数
- ❌ 错误：lynx.createCanvasContext() - 已废弃API

**步骤2 - 设置resize事件监听 (必须在绑定前)**：
- ✅ 正确：canvas.addEventListener('resize', callback)
- ❌ 错误：直接使用canvas.width/canvas.height - 没有resize监听
- ❌ 错误：在attachToCanvasView后设置resize - 时机错误

**步骤3 - 设备像素比(DPR)处理 (防止模糊)**：

🔍 DPR处理的两个关键阶段：

**阶段1 - 初始化（乘以pixelRatio）**：
- ✅ 正确：canvas.width = width * SystemInfo.pixelRatio
- ✅ 正确：canvas.height = height * SystemInfo.pixelRatio
- ✅ 正确：const pixelRatio = SystemInfo.pixelRatio || 1

**阶段2 - 绘图（不乘以pixelRatio）**：
- ✅ 正确：ctx.scale(pixelRatio, pixelRatio)
- ✅ 正确：之后所有绘图操作使用逻辑尺寸（不乘pixelRatio）
- ✅ 正确：ctx.clearRect(0, 0, width, height)  // 使用逻辑尺寸
- ✅ 正确：ctx.fillRect(x, y, width, height)   // 使用逻辑尺寸
- ❌ 错误：直接使用canvas.width/canvas.height进行绘图
- ❌ 错误：绘图操作中再次乘以pixelRatio

**步骤4 - 绑定到Canvas View**：
- ✅ 正确：canvas.attachToCanvasView('canvasName')
- ❌ 错误：忘记调用attachToCanvasView - Canvas不显示
- ❌ 错误：使用错误的name参数 - 绑定失败

┌─────────────────────────────────────────────────────────────┐
│ 🔍 设备像素比(DPR)处理权威指南 - 防止模糊的核心规则        │
└─────────────────────────────────────────────────────────────┘

🚨 DPR处理的两个关键阶段（必须严格遵守）：

**阶段1 - 初始化（乘以pixelRatio）**：
✅ Canvas元素尺寸：canvas.width = width * SystemInfo.pixelRatio
✅ Canvas元素高度：canvas.height = height * SystemInfo.pixelRatio
✅ 获取像素比：const pixelRatio = SystemInfo.pixelRatio || 1

**阶段2 - 绘图（不乘以pixelRatio）**：
✅ 应用缩放：ctx.scale(pixelRatio, pixelRatio)
✅ 之后所有绘图操作使用逻辑尺寸（不乘pixelRatio）
✅ 清除画布：ctx.clearRect(0, 0, width, height)
✅ 绘制图形：ctx.fillRect(x, y, width, height)
✅ 绘制文本：ctx.fillText("text", x, y)
✅ 绘制图像：ctx.drawImage(image, x, y, width, height)

🎯 常见DPR操作指南：

**坐标和尺寸设置**：
✅ 正确：ctx.fillRect(x, y, width, height)  // 使用逻辑尺寸
❌ 错误：ctx.fillRect(x * pixelRatio, y * pixelRatio, width * pixelRatio, height * pixelRatio)

**文本渲染**：
✅ 正确：ctx.font = `${fontSize}px sans-serif`  // 使用逻辑尺寸
❌ 错误：ctx.font = `${fontSize * pixelRatio}px sans-serif`

**触摸事件坐标转换**：
✅ 正确：const logicalX = touch.clientX / pixelRatio  // 物理→逻辑
❌ 错误：const logicalX = touch.clientX * pixelRatio  // 错误方向

**图像绘制**：
✅ 正确：ctx.drawImage(image, x, y, width, height)  // 使用逻辑尺寸
❌ 错误：ctx.drawImage(image, x * pixelRatio, y * pixelRatio, width * pixelRatio, height * pixelRatio)

**路径绘制**：
✅ 正确：ctx.arc(x, y, radius, 0, Math.PI * 2)  // 使用逻辑尺寸
❌ 错误：ctx.arc(x * pixelRatio, y * pixelRatio, radius * pixelRatio, 0, Math.PI * 2)

**生命周期管理**：
- 解绑：onUnload中调用detachFromCanvasView()和dispose()
- 资源管理：onHide暂停资源，onShow恢复资源，及时释放不用资源
- 性能优化：批量绘制，离屏渲染，资源主动释放dispose()

Lynx Three.js 支持：
\`\`\`javascript
const Three = require('@byted-lynx/three');
const window = Three.__scope; // Get the mocked globalThis to allow us to use browser api
const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 100);
const renderer = new THREE.WebGLRenderer({ canvas: new window.HTMLCanvasElement('GameCanvas') });
\`\`\`

**Canvas API限制与特性**：
- 不支持特性：roundrect、globalCompositeOperation、不规则shadow
- API限制：使用经过验证的Canvas方法
- WebGL抗锯齿：antialias和enableMSAA都为true才能启用MSAA
- 触摸事件：使用touchstart、touchmove、touchend
- 🔍 触摸坐标转换：logicalX = touch.clientX / pixelRatio（物理→逻辑）
- 设备适配：Canvas元素尺寸乘以pixelRatio，绘图操作使用逻辑尺寸
- 不使用2023年后的canvas新方法

**Canvas错误处理**：
- 创建失败：重试处理，适当延迟或requestAnimationFrame中重试
- Schema参数：添加&enable_canvas=1启用canvas扩展
- 随机ID：使用随机生成的id避免同名canvas冲突

🚨 CRITICAL: Canvas API混用致命错误防范
**绝对禁止在同一个Card中混用不同的Canvas API**：

🔥🔥🔥 **setupCanvas() 与 LightChart 绝对禁止混用** 🔥🔥🔥
- setupCanvas() 仅用于原生Canvas - 不能与LightChart混用
- initChart() 仅用于LightChart - 不能与原生Canvas混用
- 技术栈选择唯一 - 一个Card只能选择一种Canvas技术

❌ **绝对禁止的混用模式**：
- setupCanvas() + initChart() 在同一Card中
- lynx.createCanvasNG() + new LynxChart() 在同一Card中
- <canvas> + <lightcharts-canvas> 在同一TTML中
- 原生Canvas API + LightChart API 混用

✅ **正确的选择策略**：
方案A: 全部使用原生Canvas API + setupCanvas()
- setupCanvas() 方法初始化
- lynx.createCanvasNG() 创建Canvas
- canvas.getContext("2d") 获取上下文
- ctx.fillRect() 等原生绘制方法

方案B: 全部使用LightChart API + initChart()
- import LynxChart from "@byted/lynx-lightcharts/src/chart"
- initChart(e) 方法初始化
- new LynxChart() 创建图表实例
- chart.setOption() 配置图表

**🔥 强化错误检测规则 - 禁止Canvas和LightChart混用**：
如果代码中同时出现以下关键词组合，立即报错并要求重构：
- "setupCanvas" AND "initChart" - 绝对禁止在同一Card中
- "setupCanvas" AND "new LynxChart" - 绝对禁止混用
- "lynx.createCanvasNG" AND "LynxChart" - 技术栈冲突
- "lynx.createCanvasNG" AND "@byted/lynx-lightcharts" - API混用
- "getContext" AND "setOption" - 不同Canvas技术混用
- "canvas" 标签 AND "lightcharts-canvas" 标签 - TTML混用
- "attachToCanvasView" AND "bindinitchart" - 初始化方式混用

**根本原因**：不同Canvas API有不同的运行时依赖和环境要求，混用会导致：
- 图表渲染失败
- 运行时环境冲突
- 内存泄漏和性能问题

🔧 CRITICAL: Canvas创建和绑定规则（基于LynxCanvasRules.md）

**Canvas Element vs Canvas View 分离架构**：
- Canvas View: TTML中的<canvas>标签，在UI线程渲染
- Canvas Element: JS中的绘制对象，在JS线程执行
- 两者通过name属性关联，位于不同线程，松耦合关系

**正确的Canvas创建流程**：
\`\`\`javascript
// 创建未绑定的Canvas Element
const canvas = lynx.createCanvasNG();

// 监听resize事件（必须在绑定前设置）
canvas.addEventListener('resize', ({ width, height }) => {
  canvas.width = width * SystemInfo.pixelRatio;
  canvas.height = height * SystemInfo.pixelRatio;
  const ctx = canvas.getContext('2d');
  ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
  this.redraw();
});

// 重要！！！绑定到Canvas View
canvas.attachToCanvasView('canvasName');
\`\`\`


**Canvas尺寸设置关键规则**：
- Canvas View尺寸：通过TTML的style属性设置（rpx单位）
- Canvas Element尺寸：必须手动设置width/height（像素单位）
- 像素转换：rpx尺寸 × SystemInfo.pixelRatio = 像素尺寸
- 使用SelectorQuery获取Canvas View的实际尺寸：

❌ 错误的查询方式（使用CSS ID选择器）：
\`\`\`javascript
this.createSelectorQuery()
  .select('#canvasId')  // 错误：使用CSS ID选择器
  .invoke({
    method: 'boundingClientRect',
    success: (res) => {
      // res 可能为 null，因为无法找到 canvas 元素
      const pixelRatio = SystemInfo.pixelRatio || 1;
      canvas.width = res.width * pixelRatio;
      canvas.height = res.height * pixelRatio;
    }
  })
  .exec();
\`\`\`

**Canvas生命周期管理关键规则**：

1. **创建阶段**：
- 在onLoad或适当延迟后创建Canvas
- 确保Canvas View已完成排版再创建Canvas Element
- 如果创建失败，在requestAnimationFrame中重试

2. **绑定阶段**：
- resize事件监听必须在attachToCanvasView之前设置
- name属性必须全局唯一，避免冲突
- 检查getBoundingClientRect()返回值，null表示布局未完成

3. **使用阶段**：
- 监听resize事件，动态更新Canvas尺寸
- 使用save()/restore()隔离绘制状态
- 批量绘制操作，减少状态切换

4. **销毁阶段**：
\`\`\`javascript
onUnload() {
  if (this.canvas) {
    this.canvas.detachFromCanvasView();
    this.canvas.dispose();
    this.canvas = null;
  }
}
\`\`\`

**Canvas常见错误和解决方案**：


2. **Canvas创建返回null**：
- 原因：Canvas View未完成排版
- 解决：延迟创建或在requestAnimationFrame中重试

3. **多Canvas实例冲突**：
- 原因：name属性重复
- 解决：使用随机生成的唯一name

┌─────────────────────────────────────────────────────────────┐
│ 🚨 多Canvas实例渲染失败深度分析 - 第二个Canvas不显示      │
└─────────────────────────────────────────────────────────────┘

**🔍 多Canvas渲染失败的7大根本原因**：

**1. Canvas Name属性冲突（最常见，90%的问题）**：
❌ 问题：多个Canvas使用相同或相似的name
- 第二个同名Canvas会变成离屏Canvas，不会渲染到屏幕
- Lynx框架通过name属性关联Canvas Element和Canvas View
- 同名Canvas会导致后创建的实例覆盖前面的实例

✅ 解决方案：
\`\`\`javascript
// 错误：使用相同或相似的name
canvas1.attachToCanvasView("canvas");      // ❌
canvas2.attachToCanvasView("canvas");      // ❌ 第二个不会显示

// 正确：使用完全不同的name
canvas1.attachToCanvasView("parabolaCanvas");  // ✅
canvas2.attachToCanvasView("familyCanvas");    // ✅

// 最佳：使用随机生成的唯一name
const uniqueId1 = \`canvas-\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`;
const uniqueId2 = \`canvas-\${Date.now() + 1}-\${Math.random().toString(36).substr(2, 9)}\`;
\`\`\`

**2. Canvas初始化时机竞争（80%的问题）**：
❌ 问题：多个Canvas同时初始化导致竞争条件
- 第一个Canvas可能还未完成排版，第二个就开始初始化
- DOM排版未完成时，Canvas View的getBoundingClientRect()返回null
- 导致第二个Canvas尺寸设置失败

✅ 解决方案：
\`\`\`javascript
// 错误：同时初始化
onReady() {
  this.setupCanvas();      // ❌ 同时执行
  this.setupFamilyCanvas(); // ❌ 可能失败
}

// 正确：延迟初始化
onReady() {
  // 第一个Canvas先初始化
  this.setupCanvas();

  // 延迟初始化第二个Canvas，确保第一个完成
  setTimeout(() => {
    this.setupFamilyCanvas();
  }, 100); // 给足够时间完成排版
}

// 最佳：使用requestAnimationFrame确保DOM完成
onReady() {
  this.setupCanvas();

  requestAnimationFrame(() => {
    requestAnimationFrame(() => {
      this.setupFamilyCanvas(); // 确保在下一个渲染帧执行
    });
  });
}
\`\`\`

**3. Resize事件监听器冲突（70%的问题）**：
❌ 问题：多个Canvas的resize事件可能相互干扰
- 如果Canvas View尺寸相同，可能触发错误的resize事件
- 事件监听器绑定时机不当导致事件丢失

✅ 解决方案：
\`\`\`javascript
// 错误：没有明确区分Canvas实例
canvas.addEventListener("resize", ({ width, height }) => {
  // 不知道是哪个Canvas的resize事件
  this.canvas.width = width * SystemInfo.pixelRatio; // ❌ 可能设置错Canvas
});

// 正确：明确区分Canvas实例
setupCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.addEventListener("resize", ({ width, height }) => {
    // 明确设置当前Canvas的属性
    canvas.width = width * SystemInfo.pixelRatio;
    canvas.height = height * SystemInfo.pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 保存到不同的实例变量
    this.canvas = canvas;
    this.ctx = ctx;
    this.canvasWidth = width;
    this.canvasHeight = height;
  });
  canvas.attachToCanvasView("parabolaCanvas");
}

setupFamilyCanvas() {
  const familyCanvas = lynx.createCanvasNG();
  familyCanvas.addEventListener("resize", ({ width, height }) => {
    // 明确设置family Canvas的属性
    familyCanvas.width = width * SystemInfo.pixelRatio;
    familyCanvas.height = height * SystemInfo.pixelRatio;
    const familyCtx = familyCanvas.getContext('2d');
    familyCtx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 保存到不同的实例变量
    this.familyCanvas = familyCanvas;
    this.familyCtx = familyCtx;
    this.familyWidth = width;
    this.familyHeight = height;
  });
  familyCanvas.attachToCanvasView("familyCanvas");
}
\`\`\`

**4. Canvas View布局未完成（60%的问题）**：
❌ 问题：Canvas View在TTML中的布局尚未完成
- 第二个Canvas View可能还在布局过程中
- getBoundingClientRect()返回null或错误的尺寸
- 导致Canvas Element无法正确绑定

✅ 解决方案：
\`\`\`javascript
// 错误：不检查布局状态
setupFamilyCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.attachToCanvasView("familyCanvas"); // ❌ 可能布局未完成
}

// 正确：检查布局状态并重试
setupFamilyCanvas() {
  const canvas = lynx.createCanvasNG();

  // 检查Canvas View是否已完成布局
  const checkAndBind = () => {
    const query = lynx.createSelectorQuery();
    query.select('canvas[name="familyCanvas"]').boundingClientRect((rect) => {
      if (rect && rect.width > 0 && rect.height > 0) {
        // 布局完成，可以安全绑定
        canvas.attachToCanvasView("familyCanvas");
      } else {
        // 布局未完成，延迟重试
        setTimeout(checkAndBind, 50);
      }
    }).exec();
  };

  checkAndBind();
}
\`\`\`

**5. 内存和上下文冲突（50%的问题）**：
❌ 问题：多个Canvas共享或冲突的上下文状态
- 第一个Canvas的绘制状态影响第二个Canvas
- 内存不足导致第二个Canvas创建失败
- GPU资源竞争导致渲染异常

✅ 解决方案：
\`\`\`javascript
// 错误：共享变量导致冲突
let ctx; // ❌ 全局变量，多个Canvas会冲突

setupCanvas() {
  ctx = canvas.getContext('2d'); // ❌ 覆盖前一个Canvas的ctx
}

// 正确：独立的Canvas实例和上下文
setupCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.addEventListener("resize", ({ width, height }) => {
    canvas.width = width * SystemInfo.pixelRatio;
    canvas.height = height * SystemInfo.pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 独立保存，避免冲突
    this.mainCanvas = canvas;
    this.mainCtx = ctx;
    this.mainWidth = width;
    this.mainHeight = height;
  });
}

setupFamilyCanvas() {
  const familyCanvas = lynx.createCanvasNG();
  familyCanvas.addEventListener("resize", ({ width, height }) => {
    familyCanvas.width = width * SystemInfo.pixelRatio;
    familyCanvas.height = height * SystemInfo.pixelRatio;
    const familyCtx = familyCanvas.getContext('2d');
    familyCtx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 完全独立的实例
    this.familyCanvas = familyCanvas;
    this.familyCtx = familyCtx;
    this.familyWidth = width;
    this.familyHeight = height;
  });
}
\`\`\`

**6. TTML结构问题（40%的问题）**：
❌ 问题：TTML中Canvas标签定义不正确
- 缺少第二个Canvas标签
- Canvas标签的name属性与JS中不匹配
- Canvas标签的样式导致不可见

✅ 解决方案：
\`\`\`html
<!-- 错误：只有一个Canvas或name不匹配 -->
<canvas name="canvas" class="main-canvas"></canvas>
<!-- 缺少第二个Canvas -->

<!-- 正确：明确定义两个不同的Canvas -->
<canvas name="parabolaCanvas" class="main-canvas"></canvas>
<canvas name="familyCanvas" class="family-canvas"></canvas>

<!-- 确保CSS样式不会隐藏Canvas -->
.main-canvas, .family-canvas {
  width: 100%;
  height: 300rpx;
  display: block; /* 确保可见 */
  visibility: visible; /* 确保不被隐藏 */
}
\`\`\`

**7. 绘制函数调用时机错误（30%的问题）**：
❌ 问题：第二个Canvas的绘制函数在Canvas准备好之前调用
- resize事件还未触发就调用绘制函数
- Canvas尺寸为0时调用绘制函数
- 上下文未初始化就开始绘制

✅ 解决方案：
\`\`\`javascript
// 错误：立即调用绘制函数
setupFamilyCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.attachToCanvasView("familyCanvas");
  this.drawParabolaFamily(); // ❌ Canvas还未准备好
}

// 正确：在resize事件中调用绘制函数
setupFamilyCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.addEventListener("resize", ({ width, height }) => {
    if (width > 0 && height > 0) { // 确保尺寸有效
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

      this.familyCanvas = canvas;
      this.familyCtx = ctx;
      this.familyWidth = width;
      this.familyHeight = height;

      // 在Canvas完全准备好后才绘制
      this.drawParabolaFamily();
    }
  });
  canvas.attachToCanvasView("familyCanvas");
}
\`\`\`

**🎯 多Canvas最佳实践模板**：

\`\`\`javascript
// 完整的多Canvas解决方案模板
Card({
  data: {
    // 数据定义
  },

  // 独立的Canvas实例变量
  mainCanvas: null,
  mainCtx: null,
  mainWidth: 0,
  mainHeight: 0,

  familyCanvas: null,
  familyCtx: null,
  familyWidth: 0,
  familyHeight: 0,

  pixelRatio: 1,

  onReady() {
    // 延迟初始化，确保DOM完成排版
    setTimeout(() => {
      this.setupMainCanvas();

      // 第二个Canvas延迟更长时间
      setTimeout(() => {
        this.setupFamilyCanvas();
      }, 150);
    }, 100);
  },

  setupMainCanvas() {
    console.log('初始化主Canvas...');
    try {
      const canvas = lynx.createCanvasNG();

      canvas.addEventListener("resize", ({ width, height }) => {
        if (width > 0 && height > 0) {
          console.log('主Canvas resize:', width, height);

          const pixelRatio = SystemInfo.pixelRatio || 1;
          canvas.width = width * pixelRatio;
          canvas.height = height * pixelRatio;

          const ctx = canvas.getContext('2d');
          ctx.scale(pixelRatio, pixelRatio);

          // 保存到独立变量
          this.mainCanvas = canvas;
          this.mainCtx = ctx;
          this.mainWidth = width;
          this.mainHeight = height;
          this.pixelRatio = pixelRatio;

          console.log('主Canvas初始化完成');
          this.drawMainContent();
        }
      });

      canvas.attachToCanvasView("mainCanvas");
    } catch (error) {
      console.error('主Canvas初始化失败:', error);
    }
  },

  setupFamilyCanvas() {
    console.log('初始化家族Canvas...');
    try {
      const familyCanvas = lynx.createCanvasNG();

      familyCanvas.addEventListener("resize", ({ width, height }) => {
        if (width > 0 && height > 0) {
          console.log('家族Canvas resize:', width, height);

          const pixelRatio = SystemInfo.pixelRatio || 1;
          familyCanvas.width = width * pixelRatio;
          familyCanvas.height = height * pixelRatio;

          const familyCtx = familyCanvas.getContext('2d');
          familyCtx.scale(pixelRatio, pixelRatio);

          // 保存到独立变量
          this.familyCanvas = familyCanvas;
          this.familyCtx = familyCtx;
          this.familyWidth = width;
          this.familyHeight = height;

          console.log('家族Canvas初始化完成');
          this.drawFamilyContent();
        }
      });

      familyCanvas.attachToCanvasView("familyCanvas");
    } catch (error) {
      console.error('家族Canvas初始化失败:', error);
    }
  },

  drawMainContent() {
    if (!this.mainCtx || this.mainWidth <= 0 || this.mainHeight <= 0) {
      console.warn('主Canvas未准备好');
      return;
    }

    const ctx = this.mainCtx;
    const width = this.mainWidth;
    const height = this.mainHeight;

    // 绘制主要内容
    ctx.clearRect(0, 0, width, height);
    // ... 具体绘制逻辑
  },

  drawFamilyContent() {
    if (!this.familyCtx || this.familyWidth <= 0 || this.familyHeight <= 0) {
      console.warn('家族Canvas未准备好');
      return;
    }

    const ctx = this.familyCtx;
    const width = this.familyWidth;
    const height = this.familyHeight;

    // 绘制家族内容
    ctx.clearRect(0, 0, width, height);
    // ... 具体绘制逻辑
  },

  onUnload() {
    // 清理所有Canvas资源
    if (this.mainCanvas) {
      this.mainCanvas.detachFromCanvasView();
      this.mainCanvas.dispose();
      this.mainCanvas = null;
      this.mainCtx = null;
    }

    if (this.familyCanvas) {
      this.familyCanvas.detachFromCanvasView();
      this.familyCanvas.dispose();
      this.familyCanvas = null;
      this.familyCtx = null;
    }
  }
});
\`\`\`

**🔍 多Canvas问题诊断检查清单**：

**TTML检查**：
□ 1. 确认TTML中有两个不同name的Canvas标签
□ 2. 确认Canvas标签的name与JS中attachToCanvasView参数完全匹配
□ 3. 确认Canvas标签有正确的CSS样式（width, height, display: block）
□ 4. 确认Canvas标签没有被其他元素遮挡或隐藏

**JavaScript检查**：
□ 5. 确认使用了不同的变量名保存Canvas实例
□ 6. 确认第二个Canvas延迟初始化（至少100ms）
□ 7. 确认每个Canvas都有独立的resize事件监听器
□ 8. 确认绘制函数只在resize事件中调用
□ 9. 确认Canvas尺寸检查（width > 0 && height > 0）
□ 10. 确认使用了不同的attachToCanvasView参数

**调试检查**：
□ 11. 检查控制台是否有Canvas相关错误
□ 12. 检查第二个Canvas的resize事件是否触发
□ 13. 检查第二个Canvas的width/height是否正确设置
□ 14. 检查第二个Canvas的上下文是否成功获取
□ 15. 检查绘制函数是否被调用且无错误

**常见错误模式检测**：
□ 16. 🚨 检查是否有相同的Canvas name
□ 17. 🚨 检查是否同时初始化多个Canvas
□ 18. 🚨 检查是否在resize事件外调用绘制函数
□ 19. 🚨 检查是否使用了共享的变量名
□ 20. 🚨 检查是否缺少Canvas尺寸验证

**🎯 多Canvas问题解决优先级**：

**P0 - 立即检查（90%问题）**：
1. Canvas name属性是否完全不同
2. 是否延迟初始化第二个Canvas
3. TTML中是否有对应的Canvas标签

**P1 - 重点检查（70%问题）**：
4. resize事件是否独立处理
5. Canvas变量是否独立保存
6. 绘制函数调用时机是否正确

**P2 - 深度检查（50%问题）**：
7. 内存和上下文是否冲突
8. 布局完成状态检查
9. 错误处理和重试机制

**记住**：多Canvas问题通常是多个因素叠加的结果，需要系统性排查！

4. **息屏后Canvas白屏（Android）**：
- 原因：TextureView context被清除
- 解决：在onShow中重新设置visibility

5. **Canvas尺寸为0导致渲染异常**：
- 原因：未正确设置width/height
- 解决：确保Canvas Element尺寸设置正确

**Canvas性能优化规则**：
- 使用Canvas-NG（Krypton引擎）获得GPU线程渲染
- 复杂静态内容使用离屏Canvas预渲染
- 批量处理相同状态的绘制操作
- 及时调用dispose()释放资源
- 在onHide中暂停资源，onShow中恢复

**Canvas高级绘图系统 (原生渲染) - 参考LynxCanvasAudio.ts获取完整示例**

🔍 关键DPR处理要点：
- Canvas元素尺寸：canvas.width = width * pixelRatio（物理像素）
- 绘图操作：使用逻辑尺寸（width, height），不再乘以pixelRatio
- 触摸坐标：logicalX = touch.clientX / pixelRatio（物理→逻辑转换）

🔧 CRITICAL: Canvas 查询规则（基于 Lynx 框架特性）

**Canvas 元素查询的根本问题**：
Lynx 框架虽然借鉴了许多 Web 开发的概念，但它拥有自己独特的 API 和实现规范。一个常见的错误来源是开发者将标准的 Web API 使用习惯直接应用于 Lynx 环境，而未查阅其特定文档，从而导致兼容性问题。

**核心根源**：
开发者试图使用标准的 CSS ID 选择器（例如 #my-canvas）来查询一个需要通过 Lynx 框架特定选择器才能定位的 Canvas 组件。
在 Lynx 中，<canvas> 组件的标识符是 canvas-id，它并非 HTML 标准中的 id 属性。因此，标准的 document.querySelector('#id') 或 Lynx 提供的 query.select('#id') 语法无法正确定位到该组件。


❌ 错误示例 - 使用标准 CSS ID 选择器：
\`\`\`javascript
Page({
  onReady() {
    const query = lynx.createSelectorQuery();

    // 错误：使用 CSS ID 选择器 '#some-canvas-id'
    query.select('#some-canvas-id').fields({ node: true, size: true }).exec((res) => {
      if (res && res[0]) {
        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        // ...后续绘图操作将失败，因为 canvas 为空
      } else {
        console.error("获取 Canvas 节点失败！");
      }
    });
  }
})
\`\`\`

✅ 正确示例 使用canvas.attachToCanvasView绑定到TTML的Canvas视图
**TTML结构**：
\`\`\`html
<!-- Canvas 动画区域 -->
<canvas
  name="canvas-llm"
  class="canvas-llm"
></canvas>
\`\`\`

**JavaScript初始化**：
\`\`\`javascript
// 初始化Canvas
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    // 重要：resize事件监听必须在绑定前设置
    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      console.log('Canvas setup complete, starting animation...');
      this.startAnimation();
    });

    // 绑定到Canvas视图
    canvas.attachToCanvasView("canvas-llm");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}
\`\`\`

**完整的DPR处理示例代码**：
\`\`\`javascript
// 标准Canvas初始化流程 - 完整DPR处理
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    // 重要：resize事件监听必须在绑定前设置
    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);

      // 🔍 设备像素比(DPR)处理 - 阶段1：初始化（乘以pixelRatio）
      const pixelRatio = SystemInfo.pixelRatio || 1;
      canvas.width = width * pixelRatio;   // 设置物理像素宽度
      canvas.height = height * pixelRatio; // 设置物理像素高度

      // 🔍 设备像素比(DPR)处理 - 阶段2：绘图（应用缩放后使用逻辑尺寸）
      const ctx = canvas.getContext('2d');
      ctx.scale(pixelRatio, pixelRatio);   // 应用缩放

      // 保存引用（重要：保存逻辑尺寸用于绘图操作）
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;   // 逻辑宽度（不乘pixelRatio）
      this.canvasHeight = height; // 逻辑高度（不乘pixelRatio）
      this.pixelRatio = pixelRatio;

      console.log('Canvas setup complete, starting animation...');
      this.startAnimation();
    });

    // 绑定到Canvas视图
    canvas.attachToCanvasView("canvas-name");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}

// DPR处理的绘图示例
drawContent() {
  const { ctx, canvasWidth, canvasHeight } = this;
  if (!ctx) return;

  // 🔍 重要：使用逻辑尺寸（canvasWidth, canvasHeight），不使用canvas.width/canvas.height

  // 清除画布（使用逻辑尺寸）
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);

  // 绘制背景（使用逻辑尺寸）
  ctx.fillStyle = '#f8f9fa';
  ctx.fillRect(0, 0, canvasWidth, canvasHeight);

  // 绘制文本（使用逻辑尺寸）
  const fontSize = Math.max(12, Math.min(canvasWidth, canvasHeight) / 30);
  ctx.font = \`\${fontSize}px sans-serif\`;  // 不需要乘以pixelRatio
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillStyle = '#333';
  ctx.fillText('Hello Canvas', canvasWidth / 2, canvasHeight / 2);

  // 绘制圆形（使用逻辑尺寸）
  ctx.beginPath();
  ctx.arc(canvasWidth / 2, canvasHeight / 2 + fontSize * 2, fontSize, 0, Math.PI * 2);
  ctx.fillStyle = '#007bff';
  ctx.fill();
}

// 触摸事件处理 - 正确的DPR坐标转换
setupCanvasInteraction() {
  this.canvas.addEventListener("touchstart", (event) => {
    if (event.changedTouches) {
      const touch = event.changedTouches[0];

      // 🔍 正确的坐标转换：从物理像素转换为逻辑像素
      const logicalX = touch.clientX / this.pixelRatio;
      const logicalY = touch.clientY / this.pixelRatio;

      // 检查是否在Canvas范围内（使用逻辑尺寸）
      const isInBounds = logicalX >= 0 && logicalX <= this.canvasWidth &&
                        logicalY >= 0 && logicalY <= this.canvasHeight;

      if (isInBounds) {
        this.onTouchCanvas(logicalX, logicalY);
      }
    }
  });
}
\`\`\`

**Canvas 视觉增强技术**：

**实用视觉增强**：
- 信息可视化：数据驱动的图表、图形、指示器
- 状态反馈：加载进度、操作状态、错误提示
- 导航辅助：高亮、指引、路径标识
- 内容组织：分组框架、连接线、层次标识

**精美动画效果**：
- 过渡动画：状态切换的平滑过渡，300-500ms
- 反馈动画：点击确认、悬停提示、拖拽跟随
- 引导动画：新功能介绍、操作提示
- 数据动画：图表更新、数值变化展示

**移动端优化要求**：
- 卡片样式优化：为主要指标卡片和图表背景添加圆角和渐变和高光效果
- 趋势图表增强：
  - 增加Y轴的网格线和刻度标签，使数据更易于解读
  - 优化数据点和标签的显示逻辑，禁止出现文字重叠
  - 调整图表的内边距和整体布局，使其不那么拥挤
  - 图表需要增加详细的图例说明，包含各项的名称、数值和百分比
- 动态字体大小：标题和标签的字体大小，要根据画布的宽度和高度进行计算，确保在画布缩放时文字大小能相应调整
- 最小字体限制：为字体大小设置一个最小值（12px），防止在画布过小时文字变得难以阅读
- 相对布局：标签的X、Y位置以及行高也要相对于画布尺寸和字体大小进行计算，使得整体布局更具适应性

**Canvas 开发关键点**：
- 必须显式绑定/解绑: attachToCanvasView/detachFromCanvasView
- 通过addEventListener("resize")获取实际尺寸并更新canvas宽高
- 销毁时必须解绑并清理引用
- 增加充分的 try catch 和属性 fallback 以防止兼容性错误，并打印充足的 console.log 进行 debug
- 标签使用name="canvas-llm"（不是id）
- 所有操作乘以SystemInfo.pixelRatio
- 避免新Canvas API（如roundRect和globalCompositeOperation）

🔥 **Canvas 查询规则总结**：
**永远记住**：在 Lynx 框架中进行 Canvas 查询时：
1. **TTML 中使用**：canvas-id="your-canvas-id" 属性
2. **JavaScript 查询时使用**：'canvasId=your-canvas-id' 选择器语法
3. **绝对不要使用**：'#your-canvas-id' CSS ID 选择器
4. **这是 Lynx 框架的特有实现**，与标准 Web API 不同，必须严格遵循

**记忆口诀**：canvas-id 对应 canvasId=，查询成功靠语法！

🚨🚨🚨 CLAUDE4 CANVAS错误防范总结 🚨🚨🚨

**最高频错误 - 错误的Canvas初始化模式**：
❌ Claude4经常犯的错误：
\`\`\`javascript
// 错误示例：缺少关键步骤的Canvas初始化
initRouteCanvas() {
  const canvas = lynx.createCanvasNG("routeCanvas");  // ❌ 错误1：传参数
  if (!canvas) return;

  const ctx = canvas.getContext("2d");
  const width = canvas.width;                         // ❌ 错误2：没有resize监听
  const height = canvas.height;                       // ❌ 错误3：没有pixelRatio适配

  ctx.clearRect(0, 0, width, height);                 // ❌ 错误4：没有attachToCanvasView
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);
  this.drawSilkRoadMap(ctx, width, height);
}

// 或者使用已废弃的API
const canvas = lynx.createCanvas("canvasName");       // ❌ 错误5：使用废弃API
\`\`\`

✅ 正确的Canvas初始化：
\`\`\`javascript
setupCanvas() {
  console.log('Setting up canvas...');
  try {
    const canvas = lynx.createCanvasNG();

    canvas.addEventListener("resize", ({ width, height }) => {
      console.log('Canvas resize event:', width, height);
      canvas.width = width * SystemInfo.pixelRatio;
      canvas.height = height * SystemInfo.pixelRatio;
      const ctx = canvas.getContext('2d');
      ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
      this.canvas = canvas;
      this.ctx = ctx;
      this.canvasWidth = width;
      this.canvasHeight = height;
      this.startAnimation();
    });

    canvas.attachToCanvasView("canvas-llm");
  } catch (error) {
    console.error('Canvas setup failed:', error);
  }
}
\`\`\`

✅ 强制要求的正确模式：
\`\`\`javascript
// 正确示例：完整的Canvas初始化流程
initRouteCanvas() {
  // 步骤1：创建Canvas Element（无参数）
  const canvas = lynx.krypton.createCanvasNG();

  // 步骤2：设置resize事件监听（必须在绑定前）
  canvas.addEventListener('resize', ({ width, height }) => {
    // 步骤3：pixelRatio适配（防止模糊）
    canvas.width = width * SystemInfo.pixelRatio;
    canvas.height = height * SystemInfo.pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);

    // 绘制逻辑
    this.drawSilkRoadMap(ctx, width, height);
  });

  // 步骤4：绑定到Canvas View
  canvas.attachToCanvasView('routeCanvas');
},

drawSilkRoadMap(ctx, width, height) {
  ctx.clearRect(0, 0, width, height);
  ctx.fillStyle = "#f8f9fa";
  ctx.fillRect(0, 0, width, height);
  // 其他绘制逻辑...
}
\`\`\`

**🔥 强制检查清单 - Canvas和LightChart分离验证**：

**原生Canvas专用检查（使用setupCanvas()）**：
□ 1. lynx.createCanvasNG() - 无参数创建（不是lynx.createCanvas）
□ 2. addEventListener('resize') - resize事件监听在绑定前设置
□ 3. attachToCanvasView(name) - 绑定到Canvas View
□ 4. 使用setupCanvas()方法进行初始化
□ 5. 绘制逻辑在resize回调中执行
□ 6. 🚨 确认没有LightChart相关代码（new LynxChart、chart.setOption等）

**🔍 设备像素比(DPR)处理专项检查**：
□ 7. 阶段1-初始化：canvas.width = width * SystemInfo.pixelRatio
□ 8. 阶段1-初始化：canvas.height = height * SystemInfo.pixelRatio
□ 9. 阶段2-绘图：ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio)
□ 10. 保存逻辑尺寸：this.canvasWidth = width（不乘pixelRatio）
□ 11. 保存逻辑尺寸：this.canvasHeight = height（不乘pixelRatio）
□ 12. 绘图操作使用逻辑尺寸：ctx.clearRect(0, 0, width, height)
□ 13. 文本渲染使用逻辑尺寸：ctx.font = \`\${fontSize}px sans-serif\`
□ 14. 触摸坐标转换：logicalX = touch.clientX / pixelRatio
□ 15. 🚨 确认绘图操作中没有再次乘以pixelRatio

**LightChart专用检查（使用initChart()）**：
□ 1. import LynxChart from "@byted/lynx-lightcharts/src/chart"
□ 2. initChart(e) 方法接收事件参数
□ 3. new LynxChart({ canvasName, width, height })
□ 4. chart.setOption(option) 配置图表
□ 5. chart.destroy() 在onUnload中销毁
□ 6. <lightcharts-canvas> 标签使用
□ 7. 🚨 确认没有原生Canvas相关代码（lynx.createCanvasNG、setupCanvas等）

**混用检测（必须为空）**：
□ 8. 🔥 确认同一Card中没有setupCanvas() AND initChart()
□ 9. 🔥 确认同一Card中没有原生Canvas AND LightChart API

**违反任何一条都会导致**：
- Canvas不显示或显示异常
- 尺寸错误或模糊显示
- resize时不重绘
- 高分屏适配失败

**记住**：Canvas初始化是4步骤流程，缺一不可！`;
