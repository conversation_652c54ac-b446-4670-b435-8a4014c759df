// -----------------------------------------------------------------------------
// queryParser.ts
// -----------------------------------------------------------------------------
// 该工具文件仅包含一个用于解析用户输入查询列表的纯函数：parseQueryList。
// 之所以采用单独文件，是为了让解析逻辑可以在
//  1) 组件 (QueryInputPanel)
//  2) 服务层 (BatchProcessorService)
//  3) 单元测试
// 中被重复复用，而不产生依赖循环。
// -----------------------------------------------------------------------------

/**
 * 支持的分隔符：
 *   1. 换行符 (\n / \r\n)
 *   2. 逗号 (,)
 *   3. 中文逗号 (，)
 *   4. 分号 (;)
 *   5. 空格 ( )
 * 我们的解析策略非常简单：先统一替换为换行符，然后按行拆分，最后去掉空白项。
 * 之所以没有使用更复杂的正则一次搞定，是为了在后期需要扩展分隔符时更容易维护。
 */
export function parseQueryList(raw: string): string[] {
  // 0️⃣ 边界检查 -----------------------------------------------------------------
  if (!raw) {
    console.log('[queryParser] 输入为空，返回空数组');
    return [];
  }

  console.log('[queryParser] 开始解析查询列表，原始输入:', raw);

  // 1️⃣ 统一分隔符 ----------------------------------------------------------------
  //   - 将各种分隔符转换为标准的换行符，以便之后 split('\n')
  //   - 分隔符列表如果后期需要扩展，可集中维护在此处
  const NORMALIZED = raw
    .replace(/\r?\n/g, '\n') // Windows 回车换行 -> 单换行
    .replace(/[;,，]/g, '\n') // 逗号 / 中文逗号 / 分号 -> 换行
    .replace(/\s+/g, ' ') // 将多空格折叠为单空格，防止意外生成空项
    .replace(/\s+\n/g, '\n') // 空格后紧跟换行 -> 直接换行
    .replace(/\n\s+/g, '\n'); // 换行后紧跟空格 -> 直接换行

  console.log('[queryParser] 标准化后的文本:', NORMALIZED);

  // 修复：空格作为分隔符处理，将空格也转换为换行符
  const fixedNormalized = NORMALIZED.replace(/ /g, '\n');
  console.log('[queryParser] 空格分隔符修复后的文本:', fixedNormalized);

  // 2️⃣ 拆分、去重、去空 -----------------------------------------------------------
  const items = fixedNormalized
    .split('\n')
    .map(item => item.trim()) // 去除左右空格
    .filter(item => item.length > 0); // 过滤空字符串

  console.log('[queryParser] 拆分后的查询项目:', items);

  // 3️⃣ 最终去重（保持顺序） -------------------------------------------------------
  const seen = new Set<string>();
  const uniqueItems: string[] = [];
  for (const item of items) {
    if (!seen.has(item)) {
      seen.add(item);
      uniqueItems.push(item);
    }
  }

  // DEBUG LOG --------------------------------------------------------------------
  // 生产环境可以考虑通过环境变量或全局配置开关关闭日志
  console.log('[queryParser] 原始输入长度:', raw.length);
  console.log('[queryParser] 解析后条目数:', uniqueItems.length);
  console.log('[queryParser] 最终解析结果:', uniqueItems);
  console.table(uniqueItems.slice(0, 10));

  return uniqueItems;
}

// -----------------------------------------------------------------------------
// 默认导出 (便于快速导入)
// -----------------------------------------------------------------------------
export default parseQueryList;
